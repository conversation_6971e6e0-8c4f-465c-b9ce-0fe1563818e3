package service_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/service"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/testutil/testsetup"
	"llif.org/wxcache/pkg/wxtypes"
)

func TestEnvironmentServiceReturnsDataIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		ctx = context.Background()
		svc = service.NewEnvironmentService(c)

		timeTo   = time.Now().UTC()
		timeFrom = timeTo.Add(-24 * time.Hour)

		spacetime = make([]wxtypes.SpaceTime, 0)
	)

	for _, loc := range location.GetAll() {
		spacetime = append(spacetime, wxtypes.SpaceTime{
			TimeFrom: timeFrom,
			TimeTo:   timeTo,
			Lat:      loc.Lat,
			Long:     loc.Long,
		})
	}

	t.Run("Air quality", func(t *testing.T) {
		data, err := svc.GetAirQuality(ctx, spacetime)
		require.NoError(t, err)
		require.NotEmpty(t, data)
	})

	t.Run("Weather", func(t *testing.T) {
		data, err := svc.GetWeather(ctx, spacetime)
		require.NoError(t, err)
		require.NotEmpty(t, data)
	})

	t.Run("Pollen", func(t *testing.T) {
		data, err := svc.GetPollen(ctx, spacetime)
		require.NoError(t, err)
		require.NotEmpty(t, data)
	})
}

func TestEnvironmentServiceReturnsForecastDataIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	c, err := testsetup.NewConfig()
	require.NoError(t, err)
	max_hourly_bucket_count := 2 * 24

	var (
		ctx = context.Background()
		svc = service.NewEnvironmentService(c)

		timeTo   = time.Now().UTC().Add(time.Duration(max_hourly_bucket_count) * time.Hour)
		timeFrom = time.Now().UTC()

		spacetime = make([]wxtypes.SpaceTime, 0)
	)

	for _, loc := range location.GetAll() {
		spacetime = append(spacetime, wxtypes.SpaceTime{
			TimeFrom: timeFrom,
			TimeTo:   timeTo,
			Lat:      loc.Lat,
			Long:     loc.Long,
		})
	}

	t.Run("Forecast air quality", func(t *testing.T) {
		for _, st := range spacetime {
			data, err := svc.GetAirQualityForecast(ctx, []wxtypes.SpaceTime{st})
			require.NoError(t, err)
			require.NotEmpty(t, data)
			require.GreaterOrEqual(t, len(data), max_hourly_bucket_count)
		}
	})

	t.Run("Forecast weather", func(t *testing.T) {
		for _, st := range spacetime {
			data, err := svc.GetWeatherForecast(ctx, []wxtypes.SpaceTime{st})
			require.NoError(t, err)
			require.NotEmpty(t, data)
			require.GreaterOrEqual(t, len(data), max_hourly_bucket_count)
		}
	})

	t.Run("Forecast pollen", func(t *testing.T) {
		for _, st := range spacetime {
			data, err := svc.GetPollenForecast(ctx, []wxtypes.SpaceTime{st})
			require.NoError(t, err)
			require.NotEmpty(t, data)
			require.GreaterOrEqual(t, len(data), max_hourly_bucket_count)
		}
	})
}
