package api

import (
	"context"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"llif.org/wxcache/internal/ckey"
)

// requestContextMiddleware adds a request ID to the context of the request
func requestContextMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			ctx := c.Request().Context()
			ctx = context.WithValue(ctx, ckey.RequestID, uuid.New().String())
			ctx = context.WithValue(ctx, ckey.Runtime, ckey.RuntimeHTTP)
			c.SetRequest(c.Request().Clone(ctx))
			return next(c)
		}
	}
}
