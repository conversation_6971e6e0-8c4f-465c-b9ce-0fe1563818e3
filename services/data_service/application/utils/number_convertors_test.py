import pytest

from services.data_service.application.utils.number_convertors import replace_nan_with_none


@pytest.fixture
def empty_dict_input() -> dict:
    return {}


@pytest.fixture
def empty_dict_output():
    return {}


@pytest.fixture
def dict_values_no_nans_input() -> dict:
    return {"a": 1, "b": 2}


@pytest.fixture
def dict_values_no_nans_output():
    return {"a": 1, "b": 2}


@pytest.fixture
def dict_with_nans_single_level_input() -> dict:
    return {"a": "NaN", "b": "NaN", "c": 1}


@pytest.fixture
def dict_with_nans_single_level_output():
    return {"a": None, "b": None, "c": 1}


@pytest.fixture
def dict_with_nans_one_level_deep_input() -> dict:
    return {"a": "NaN", "b": {"c": 0, "d": "NaN"}}


@pytest.fixture
def dict_with_nans_one_level_deep_output():
    return {"a": None, "b": {"c": 0, "d": None}}


@pytest.fixture
def dict_with_nans_3_level_deep_input() -> dict:
    return {"a": {"b": {"c": {"d": "NaN"}}}, "e": "NaN"}


@pytest.fixture
def dict_with_nans_3_level_deep_output():
    return {"a": {"b": {"c": {"d": None}}}, "e": None}


def test_replace_nan_with_none_empty_dict(empty_dict_input, empty_dict_output):
    assert replace_nan_with_none(empty_dict_input) == empty_dict_output


def test_replace_nan_with_none_no_nans_in_dict(dict_values_no_nans_input, dict_values_no_nans_output):
    assert replace_nan_with_none(dict_values_no_nans_input) == dict_values_no_nans_output


def test_replace_nan_with_none_single_level_dict(dict_with_nans_single_level_input, dict_with_nans_single_level_output):
    assert replace_nan_with_none(dict_with_nans_single_level_input) == dict_with_nans_single_level_output


def test_replace_nan_with_none_one_level_deep_dict(
    dict_with_nans_one_level_deep_input, dict_with_nans_one_level_deep_output
):
    assert replace_nan_with_none(dict_with_nans_one_level_deep_input) == dict_with_nans_one_level_deep_output


def test_replace_nan_with_deeper_nesting_nan_dict(
    dict_with_nans_3_level_deep_input, dict_with_nans_3_level_deep_output
):
    assert replace_nan_with_none(dict_with_nans_3_level_deep_input) == dict_with_nans_3_level_deep_output
