from abc import ABC
from typing import Literal

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.annotated_types import Rounded6Float
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.nutrition.drink import DrinkCategory, DrinkIdentifier
from services.base.domain.schemas.events.nutrition.food import FoodCategory, FoodIdentifier
from services.base.domain.schemas.events.nutrition.nutrients import Nutrients
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionValueLimits
from services.base.domain.schemas.events.nutrition.supplement import SupplementCategory, SupplementIdentifier
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_nutrition_template_inputs import (
    InsertDrinkTemplateInput,
    InsertFoodTemplateInput,
    InsertSupplementTemplateInput,
)


class InsertNutritionTemplateInputBuilderBase(InsertTemplateInputBuilderBase, ABC):
    def __init__(self):
        super().__init__()
        self._brand: str | None = None
        self._rating: int | None = None
        self._items_per_serving: Rounded6Float | None = None
        self._consumed_amount: Rounded6Float | None = None
        self._amount: Rounded6Float | None = None
        self._calories: Rounded6Float | None = None
        self._flavor: str | None = None
        self._nutrients: Nutrients | None = None

    def _get_nutrition_base_fields(self) -> dict:
        """Get common fields for all nutrition template inputs"""
        base_fields = self._get_base_fields()
        base_fields.update(
            {
                "brand": self._brand
                or PrimitiveTypesGenerator.generate_random_string(max_length=NutritionValueLimits.MAX_BRAND_NAME),
                "rating": self._rating
                or PrimitiveTypesGenerator.generate_random_int(
                    min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                    max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
                    allow_none=True,
                ),
                "items_per_serving": self._items_per_serving
                or PrimitiveTypesGenerator.generate_random_float(
                    min_value=NutritionValueLimits.MIN_ITEMS_PER_SERVING,
                    max_value=NutritionValueLimits.MAX_ITEMS_PER_SERVING,
                    allow_none=True,
                ),
                "consumed_amount": self._consumed_amount
                or PrimitiveTypesGenerator.generate_random_float(
                    min_value=NutritionValueLimits.MIN_CONSUMED_AMOUNT,
                    max_value=NutritionValueLimits.MAX_CONSUMED_AMOUNT,
                ),
                "amount": self._amount
                or PrimitiveTypesGenerator.generate_random_float(
                    min_value=NutritionValueLimits.MIN_AMOUNT,
                    max_value=NutritionValueLimits.MAX_AMOUNT,
                ),
                "calories": self._calories
                or PrimitiveTypesGenerator.generate_random_float(
                    min_value=NutritionValueLimits.MIN_CALORIES,
                    max_value=NutritionValueLimits.MAX_CALORIES,
                    allow_none=True,
                ),
                "flavor": self._flavor
                or PrimitiveTypesGenerator.generate_random_string(max_length=NutritionValueLimits.MAX_FLAVOUR_LENGTH),
                "nutrients": self._nutrients,
            }
        )
        return base_fields

    def with_brand(self, brand: str | None):
        self._brand = brand
        return self

    def with_rating(self, rating: int | None):
        self._rating = rating
        return self

    def with_items_per_serving(self, items_per_serving: Rounded6Float | None):
        self._items_per_serving = items_per_serving
        return self

    def with_consumed_amount(self, consumed_amount: Rounded6Float):
        self._consumed_amount = consumed_amount
        return self

    def with_amount(self, amount: Rounded6Float):
        self._amount = amount
        return self

    def with_calories(self, calories: Rounded6Float | None):
        self._calories = calories
        return self

    def with_flavor(self, flavor: str | None):
        self._flavor = flavor
        return self

    def with_nutrients(self, nutrients: Nutrients | None):
        self._nutrients = nutrients
        return self


class InsertDrinkTemplateInputBuilder(InsertNutritionTemplateInputBuilderBase, DrinkIdentifier):
    def __init__(self):
        super().__init__()
        self._category: DrinkCategory | None = None
        self._consumed_type: VolumeUnit | Literal["item", "serving"] | None = None
        self._amount_unit: VolumeUnit | None = None

    def build(self) -> InsertDrinkTemplateInput:
        base_fields = self._get_nutrition_base_fields()
        return InsertDrinkTemplateInput(
            type=DataType.Drink,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=DrinkCategory),
            consumed_type=self._consumed_type or PrimitiveTypesGenerator.generate_random_enum(enum_type=VolumeUnit),
            amount_unit=self._amount_unit or PrimitiveTypesGenerator.generate_random_enum(enum_type=VolumeUnit),
            **base_fields,
        )

    def with_category(self, category: DrinkCategory):
        self._category = category
        return self

    def with_consumed_type(self, consumed_type: VolumeUnit | Literal["item", "serving"]):
        self._consumed_type = consumed_type
        return self

    def with_amount_unit(self, amount_unit: VolumeUnit):
        self._amount_unit = amount_unit
        return self


class InsertFoodTemplateInputBuilder(InsertNutritionTemplateInputBuilderBase, FoodIdentifier):
    def __init__(self):
        super().__init__()
        self._category: FoodCategory | None = None
        self._consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"] | None = None
        self._amount_unit: WeightUnit | VolumeUnit | None = None

    def build(self) -> InsertFoodTemplateInput:
        base_fields = self._get_nutrition_base_fields()
        return InsertFoodTemplateInput(
            type=DataType.Food,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=FoodCategory),
            consumed_type=self._consumed_type or PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            amount_unit=self._amount_unit or PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            **base_fields,
        )

    def with_category(self, category: FoodCategory):
        self._category = category
        return self

    def with_consumed_type(self, consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"]):
        self._consumed_type = consumed_type
        return self

    def with_amount_unit(self, amount_unit: WeightUnit | VolumeUnit):
        self._amount_unit = amount_unit
        return self


class InsertSupplementTemplateInputBuilder(InsertNutritionTemplateInputBuilderBase, SupplementIdentifier):
    def __init__(self):
        super().__init__()
        self._category: SupplementCategory | None = None
        self._consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"] | None = None
        self._amount_unit: WeightUnit | VolumeUnit | None = None

    def build(self) -> InsertSupplementTemplateInput:
        base_fields = self._get_nutrition_base_fields()
        return InsertSupplementTemplateInput(
            type=DataType.Supplement,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=SupplementCategory),
            consumed_type=self._consumed_type or PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            amount_unit=self._amount_unit or PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            **base_fields,
        )

    def with_category(self, category: SupplementCategory):
        self._category = category
        return self

    def with_consumed_type(self, consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"]):
        self._consumed_type = consumed_type
        return self

    def with_amount_unit(self, amount_unit: WeightUnit | VolumeUnit):
        self._amount_unit = amount_unit
        return self
