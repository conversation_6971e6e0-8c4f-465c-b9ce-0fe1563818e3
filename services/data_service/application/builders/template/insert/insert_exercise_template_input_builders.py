from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.exercise.cardio import CardioCategory, CardioIdentifier
from services.base.domain.schemas.events.exercise.exercise import ExerciseCategory, ExerciseIdentifier
from services.base.domain.schemas.events.exercise.strength import StrengthCategory, StrengthIdentifier
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_exercise_template_inputs import (
    InsertCardioTemplateInput,
    InsertExerciseTemplateInput,
    InsertStrengthTemplateInput,
)


class InsertExerciseTemplateInputBuilder(InsertTemplateInputBuilderBase, ExerciseIdentifier):
    def __init__(self):
        super().__init__()
        self._category: ExerciseCategory | None = None
        self._rating: int | None = None

    def build(self) -> InsertExerciseTemplateInput:
        base_fields = self._get_base_fields()
        return InsertExerciseTemplateInput(
            type=DataType.Exercise,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=ExerciseCategory),
            rating=self._rating
            or PrimitiveTypesGenerator.generate_random_int(
                min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
                allow_none=True,
            ),
            **base_fields,
        )

    def with_category(self, category: ExerciseCategory):
        self._category = category
        return self

    def with_rating(self, rating: int | None):
        self._rating = rating
        return self


class InsertStrengthTemplateInputBuilder(InsertTemplateInputBuilderBase, StrengthIdentifier):
    def __init__(self):
        super().__init__()
        self._category: StrengthCategory | None = None
        self._count: int | None = None
        self._weight: RoundedFloat | None = None
        self._rating: int | None = None

    def build(self) -> InsertStrengthTemplateInput:
        base_fields = self._get_base_fields()
        return InsertStrengthTemplateInput(
            type=DataType.Strength,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=StrengthCategory),
            count=self._count or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=100),
            weight=self._weight or PrimitiveTypesGenerator.generate_random_float(min_value=0.0, max_value=500.0),
            rating=self._rating
            or PrimitiveTypesGenerator.generate_random_int(
                min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
                allow_none=True,
            ),
            **base_fields,
        )

    def with_category(self, category: StrengthCategory):
        self._category = category
        return self

    def with_count(self, count: int):
        self._count = count
        return self

    def with_weight(self, weight: RoundedFloat | None):
        self._weight = weight
        return self

    def with_rating(self, rating: int | None):
        self._rating = rating
        return self


class InsertCardioTemplateInputBuilder(InsertTemplateInputBuilderBase, CardioIdentifier):
    def __init__(self):
        super().__init__()
        self._category: CardioCategory | None = None
        self._distance: RoundedFloat | None = None
        self._elevation: RoundedFloat | None = None
        self._rating: int | None = None

    def build(self) -> InsertCardioTemplateInput:
        base_fields = self._get_base_fields()
        return InsertCardioTemplateInput(
            type=DataType.Cardio,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=CardioCategory),
            distance=self._distance
            or PrimitiveTypesGenerator.generate_random_float(min_value=0.0, max_value=50000.0, allow_none=True),
            elevation=self._elevation
            or PrimitiveTypesGenerator.generate_random_float(min_value=-500.0, max_value=8848.0, allow_none=True),
            rating=self._rating
            or PrimitiveTypesGenerator.generate_random_int(
                min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
                allow_none=True,
            ),
            **base_fields,
        )

    def with_category(self, category: CardioCategory):
        self._category = category
        return self

    def with_distance(self, distance: RoundedFloat | None):
        self._distance = distance
        return self

    def with_elevation(self, elevation: RoundedFloat | None):
        self._elevation = elevation
        return self

    def with_rating(self, rating: int | None):
        self._rating = rating
        return self
