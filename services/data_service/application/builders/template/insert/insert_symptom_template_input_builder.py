from typing import Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.body_location import BodyParts
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.symptom import SymptomCategory, SymptomIdentifier, SymptomValueLimits
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_symptom_template_inputs import (
    InsertSymptomTemplateInput,
)


class InsertSymptomTemplateInputBuilder(InsertTemplateInputBuilderBase, SymptomIdentifier):
    def __init__(self):
        super().__init__()
        self._category: SymptomCategory | None = None
        self._rating: int | None = None
        self._body_parts: Sequence[BodyParts] | None = None

    def build(self) -> InsertSymptomTemplateInput:
        base_fields = self._get_base_fields()
        return InsertSymptomTemplateInput(
            type=DataType.Symptom,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=SymptomCategory),
            rating=self._rating
            or PrimitiveTypesGenerator.generate_random_int(
                min_value=SymptomValueLimits.SYMPTOM_RATING_MINIMUM_VALUE,
                max_value=SymptomValueLimits.SYMPTOM_RATING_MAXIMUM_VALUE,
                allow_none=True,
            ),
            body_parts=self._body_parts
            or [
                PrimitiveTypesGenerator.generate_random_enum(enum_type=BodyParts)
                for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=3))
            ],
            **base_fields,
        )

    def with_category(self, category: SymptomCategory):
        self._category = category
        return self

    def with_rating(self, rating: int | None):
        self._rating = rating
        return self

    def with_body_parts(self, body_parts: Sequence[BodyParts]):
        self._body_parts = body_parts
        return self
