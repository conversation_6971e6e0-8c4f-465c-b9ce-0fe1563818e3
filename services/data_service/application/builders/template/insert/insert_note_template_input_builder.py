from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.note import NoteCategory, NoteIdentifier
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_note_template_inputs import (
    InsertNoteTemplateInput,
)


class InsertNoteTemplateInputBuilder(InsertTemplateInputBuilderBase, NoteIdentifier):
    def __init__(self):
        super().__init__()
        self._category: NoteCategory | None = None

    def build(self) -> InsertNoteTemplateInput:
        base_fields = self._get_base_fields()
        return InsertNoteTemplateInput(
            type=DataType.Note,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=NoteCategory),
            **base_fields,
        )

    def with_category(self, category: NoteCategory):
        self._category = category
        return self
