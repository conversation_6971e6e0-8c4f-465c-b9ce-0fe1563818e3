from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.activity import ActivityCategory, ActivityIdentifier
from services.base.domain.schemas.events.event import EventValueLimits
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_activity_template_inputs import (
    InsertActivityTemplateInput,
)


class InsertActivityTemplateInputBuilder(InsertTemplateInputBuilderBase, ActivityIdentifier):
    def __init__(self):
        super().__init__()
        self._category: ActivityCategory | None = None
        self._rating: int | None = None

    def build(self) -> InsertActivityTemplateInput:
        base_fields = self._get_base_fields()
        return InsertActivityTemplateInput(
            type=DataType.Activity,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=ActivityCategory),
            rating=self._rating
            or PrimitiveTypesGenerator.generate_random_int(
                min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
                allow_none=True,
            ),
            **base_fields,
        )

    def with_category(self, category: ActivityCategory):
        self._category = category
        return self

    def with_rating(self, rating: int | None):
        self._rating = rating
        return self
