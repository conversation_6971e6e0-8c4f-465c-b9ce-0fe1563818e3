from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.person import PersonCategory, PersonIdentifier
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_person_template_inputs import (
    InsertPersonTemplateInput,
)


class InsertPersonTemplateInputBuilder(InsertTemplateInputBuilderBase, PersonIdentifier):
    def __init__(self):
        super().__init__()
        self._category: PersonCategory | None = None
        self._rating: int | None = None
        self._contact_id: UUID | None = None

    def build(self) -> InsertPersonTemplateInput:
        base_fields = self._get_base_fields()
        return InsertPersonTemplateInput(
            type=DataType.Person,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=PersonCategory),
            rating=self._rating
            or PrimitiveTypesGenerator.generate_random_int(
                min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
                allow_none=True,
            ),
            contact_id=self._contact_id or uuid4(),
            **base_fields,
        )

    def with_category(self, category: PersonCategory):
        self._category = category
        return self

    def with_rating(self, rating: int | None):
        self._rating = rating
        return self

    def with_contact_id(self, contact_id: UUID | None):
        self._contact_id = contact_id
        return self
