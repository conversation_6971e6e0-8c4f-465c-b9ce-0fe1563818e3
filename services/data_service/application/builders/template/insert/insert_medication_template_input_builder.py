from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.annotated_types import Rounded6Float
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.medication.medication import (
    ConsumeUnit,
    MedicationCategory,
    MedicationValueLimits,
    SingleDoseInformation,
)
from services.base.domain.schemas.templates.payload.medication_template_payload import Administration, MedicationDetails
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_medication_template_inputs import (
    InsertMedicationTemplateInput,
)


class InsertMedicationTemplateInputBuilder(InsertTemplateInputBuilderBase):
    def __init__(self):
        super().__init__()
        self._category: MedicationCategory | None = None
        self._medication_details: MedicationDetails | None = None
        self._single_dose_information: SingleDoseInformation | None = None
        self._consumed_amount: Rounded6Float | None = None
        self._consume_unit: VolumeUnit | WeightUnit | ConsumeUnit | None = None

    def build(self) -> InsertMedicationTemplateInput:
        base_fields = self._get_base_fields()
        return InsertMedicationTemplateInput(
            type=DataType.Medication,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=MedicationCategory),
            medication_details=self._medication_details
            or MedicationDetails(
                administration=PrimitiveTypesGenerator.generate_random_enum(enum_type=Administration),
                brand=PrimitiveTypesGenerator.generate_random_string(),
                generic_name=PrimitiveTypesGenerator.generate_random_string(),
                rx_cuid=PrimitiveTypesGenerator.generate_random_string(),
            ),
            single_dose_information=self._single_dose_information
            or SingleDoseInformation(
                amount=PrimitiveTypesGenerator.generate_random_float(min_value=0.1, max_value=100.0),
                amount_unit=PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
                items_quantity=PrimitiveTypesGenerator.generate_random_float(min_value=0.1, max_value=100.0),
            ),
            consumed_amount=self._consumed_amount
            or PrimitiveTypesGenerator.generate_random_float(
                min_value=MedicationValueLimits.MIN_CONSUMED_QUANTITY,
                max_value=MedicationValueLimits.MAX_CONSUMED_QUANTITY,
            ),
            consume_unit=self._consume_unit or PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            **base_fields,
        )

    def with_category(self, category: MedicationCategory):
        self._category = category
        return self

    def with_medication_details(self, medication_details: MedicationDetails):
        self._medication_details = medication_details
        return self

    def with_single_dose_information(self, single_dose_information: SingleDoseInformation):
        self._single_dose_information = single_dose_information
        return self

    def with_consumed_amount(self, consumed_amount: Rounded6Float):
        self._consumed_amount = consumed_amount
        return self

    def with_consume_unit(self, consume_unit: VolumeUnit | WeightUnit | ConsumeUnit):
        self._consume_unit = consume_unit
        return self
