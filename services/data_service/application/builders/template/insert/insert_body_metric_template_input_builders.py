from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.body_metric.blood_glucose import (
    BloodGlucoseCategory,
    BloodGlucoseIdentifier,
    BloodGlucoseSpecimenSource,
    BloodGlucoseValueLimits,
)
from services.base.domain.schemas.events.body_metric.blood_pressure import (
    BloodPressureCategory,
    BloodPressureIdentifier,
    BloodPressureValueLimits,
)
from services.base.domain.schemas.events.body_metric.body_metric import (
    BodyMetricCategory,
    BodyMetricIdentifier,
    BodyMetricLimits,
)
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_body_metric_template_inputs import (
    InsertBloodGlucoseTemplateInput,
    InsertBloodPressureTemplateInput,
    InsertBodyMetricTemplateInput,
)


class InsertBodyMetricTemplateInputBuilder(InsertTemplateInputBuilderBase, BodyMetricIdentifier):
    def __init__(self):
        super().__init__()
        self._category: BodyMetricCategory | None = None
        self._value: RoundedFloat | None = None

    def build(self) -> InsertBodyMetricTemplateInput:
        base_fields = self._get_base_fields()
        return InsertBodyMetricTemplateInput(
            type=DataType.BodyMetric,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=BodyMetricCategory),
            value=self._value
            or PrimitiveTypesGenerator.generate_random_float(
                min_value=BodyMetricLimits.MINIMUM_VALUE,
                max_value=BodyMetricLimits.MAXIMUM_VALUE,
            ),
            **base_fields,
        )

    def with_category(self, category: BodyMetricCategory):
        self._category = category
        return self

    def with_value(self, value: RoundedFloat):
        self._value = value
        return self


class InsertBloodPressureTemplateInputBuilder(InsertTemplateInputBuilderBase, BloodPressureIdentifier):
    def __init__(self):
        super().__init__()
        self._category: BloodPressureCategory | None = None
        self._systolic: RoundedFloat | None = None
        self._diastolic: RoundedFloat | None = None

    def build(self) -> InsertBloodPressureTemplateInput:
        base_fields = self._get_base_fields()
        return InsertBloodPressureTemplateInput(
            type=DataType.BloodPressure,
            category=self._category or BloodPressureCategory.BLOOD_PRESSURE,
            systolic=self._systolic
            or PrimitiveTypesGenerator.generate_random_float(
                min_value=BloodPressureValueLimits.MINIMUM_SYSTOLIC,
                max_value=BloodPressureValueLimits.MAXIMUM_SYSTOLIC,
            ),
            diastolic=self._diastolic
            or PrimitiveTypesGenerator.generate_random_float(
                min_value=BloodPressureValueLimits.MINIMUM_DIASTOLIC,
                max_value=BloodPressureValueLimits.MAXIMUM_DIASTOLIC,
            ),
            **base_fields,
        )

    def with_category(self, category: BloodPressureCategory):
        self._category = category
        return self

    def with_systolic(self, systolic: RoundedFloat):
        self._systolic = systolic
        return self

    def with_diastolic(self, diastolic: RoundedFloat):
        self._diastolic = diastolic
        return self


class InsertBloodGlucoseTemplateInputBuilder(InsertTemplateInputBuilderBase, BloodGlucoseIdentifier):
    def __init__(self):
        super().__init__()
        self._category: BloodGlucoseCategory | None = None
        self._value: RoundedFloat | None = None
        self._specimen_source: BloodGlucoseSpecimenSource | None = None

    def build(self) -> InsertBloodGlucoseTemplateInput:
        base_fields = self._get_base_fields()
        return InsertBloodGlucoseTemplateInput(
            type=DataType.BloodGlucose,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=BloodGlucoseCategory),
            value=self._value
            or PrimitiveTypesGenerator.generate_random_float(
                min_value=BloodGlucoseValueLimits.MINIMUM_VALUE,
                max_value=BloodGlucoseValueLimits.MAXIMUM_VALUE,
            ),
            specimen_source=self._specimen_source or BloodGlucoseSpecimenSource.UNKNOWN,
            **base_fields,
        )

    def with_category(self, category: BloodGlucoseCategory):
        self._category = category
        return self

    def with_value(self, value: RoundedFloat):
        self._value = value
        return self

    def with_specimen_source(self, specimen_source: BloodGlucoseSpecimenSource):
        self._specimen_source = specimen_source
        return self
