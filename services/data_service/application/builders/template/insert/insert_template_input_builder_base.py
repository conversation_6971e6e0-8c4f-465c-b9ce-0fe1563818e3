from abc import ABC, abstractmethod
from typing import Self

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.annotated_types import UniqueSequenceStr


class InsertTemplateInputBuilderBase(ABC):
    def __init__(self):
        self._name: str | None = None
        self._tags: UniqueSequenceStr | None = None
        self._duration: float | None = None
        self._note: str | None = None

    @abstractmethod
    def build(self):
        raise NotImplementedError("Each builder must implement the build method.")

    def with_name(self, name: str) -> Self:
        self._name = name
        return self

    def with_tags(self, tags: UniqueSequenceStr) -> Self:
        self._tags = tags
        return self

    def with_duration(self, duration: float | None) -> Self:
        self._duration = duration
        return self

    def with_note(self, note: str | None) -> Self:
        self._note = note
        return self

    def _get_base_fields(self) -> dict:
        return {
            "name": self._name or PrimitiveTypesGenerator.generate_random_string(max_length=32),
            "tags": self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            "duration": self._duration,
            "note": self._note or PrimitiveTypesGenerator.generate_random_string(),
        }
