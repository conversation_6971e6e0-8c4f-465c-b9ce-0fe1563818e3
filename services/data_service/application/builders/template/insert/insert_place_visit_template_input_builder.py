from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.place_category import PlaceCategory
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.place_visit import PlaceVisitIdentifier
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_place_visit_template_inputs import (
    InsertPlaceVisitTemplateInput,
)


class InsertPlaceVisitTemplateInputBuilder(InsertTemplateInputBuilderBase, PlaceVisitIdentifier):
    def __init__(self):
        super().__init__()
        self._category: PlaceCategory | None = None
        self._place_id: UUID | None = None
        self._rating: int | None = None

    def build(self) -> InsertPlaceVisitTemplateInput:
        base_fields = self._get_base_fields()
        return InsertPlaceVisitTemplateInput(
            type=DataType.PlaceVisit,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=PlaceCategory),
            place_id=self._place_id or uuid4(),
            rating=self._rating
            or PrimitiveTypesGenerator.generate_random_int(
                min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
                allow_none=True,
            ),
            **base_fields,
        )

    def with_category(self, category: PlaceCategory):
        self._category = category
        return self

    def with_place_id(self, place_id: UUID | None):
        self._place_id = place_id
        return self

    def with_rating(self, rating: int | None):
        self._rating = rating
        return self
