from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.feeling.emotion import EmotionCategory, EmotionIdentifier
from services.base.domain.schemas.events.feeling.stress import (
    StressCategory,
    StressIdentifier,
    StressValueLimits,
)
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_feeling_template_inputs import (
    InsertEmotionTemplateInput,
    InsertStressTemplateInput,
)


class InsertEmotionTemplateInputBuilder(InsertTemplateInputBuilderBase, EmotionIdentifier):
    def __init__(self):
        super().__init__()
        self._category: EmotionCategory | None = None
        self._rating: int | None = None

    def build(self) -> InsertEmotionTemplateInput:
        base_fields = self._get_base_fields()
        return InsertEmotionTemplateInput(
            type=DataType.Emotion,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=EmotionCategory),
            rating=self._rating
            or PrimitiveTypesGenerator.generate_random_int(
                min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
            ),
            **base_fields,
        )

    def with_category(self, category: EmotionCategory):
        self._category = category
        return self

    def with_rating(self, rating: int):
        self._rating = rating
        return self


class InsertStressTemplateInputBuilder(InsertTemplateInputBuilderBase, StressIdentifier):
    def __init__(self):
        super().__init__()
        self._category: StressCategory | None = None
        self._rating: int | None = None

    def build(self) -> InsertStressTemplateInput:
        base_fields = self._get_base_fields()
        return InsertStressTemplateInput(
            type=DataType.Stress,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=StressCategory),
            rating=self._rating
            or PrimitiveTypesGenerator.generate_random_int(
                min_value=StressValueLimits.STRESS_MINIMUM_VALUE,
                max_value=StressValueLimits.STRESS_MAXIMUM_VALUE,
            ),
            **base_fields,
        )

    def with_category(self, category: StressCategory):
        self._category = category
        return self

    def with_rating(self, rating: int):
        self._rating = rating
        return self
