from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.sleep_v3 import SleepV3Category, SleepV3Identifier
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_sleep_template_inputs import (
    InsertSleepV3TemplateInput,
)


class InsertSleepV3TemplateInputBuilder(InsertTemplateInputBuilderBase, SleepV3Identifier):
    def __init__(self):
        super().__init__()
        self._category: SleepV3Category | None = None
        self._rating: int | None = None

    def build(self) -> InsertSleepV3TemplateInput:
        base_fields = self._get_base_fields()
        return InsertSleepV3TemplateInput(
            type=DataType.SleepV3,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=SleepV3Category),
            rating=self._rating
            or PrimitiveTypesGenerator.generate_random_int(
                min_value=EventValueLimits.RATING_MINIMUM_VALUE,
                max_value=EventValueLimits.RATING_MAXIMUM_VALUE,
                allow_none=True,
            ),
            deep_seconds=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            light_seconds=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            rem_seconds=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            awake_seconds=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            restless_moments=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            provider_score=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            llif_score=PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=10),
            **base_fields,
        )

    def with_category(self, category: SleepV3Category):
        self._category = category
        return self

    def with_rating(self, rating: int | None):
        self._rating = rating
        return self
