from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.content import (
    ContentCategory,
    ContentIdentifier,
    ContentValueLimits,
)
from services.data_service.application.builders.template.insert.insert_template_input_builder_base import (
    InsertTemplateInputBuilderBase,
)
from services.data_service.application.use_cases.templates.models.insert_content_template_input import (
    InsertContentTemplateInput,
)


class InsertContentTemplateInputBuilder(InsertTemplateInputBuilderBase, ContentIdentifier):
    def __init__(self):
        super().__init__()
        self._category: ContentCategory | None = None
        self._title: str | None = None
        self._url: str | None = None
        self._rating: int | None = None

    def build(self) -> InsertContentTemplateInput:
        base_fields = self._get_base_fields()
        return InsertContentTemplateInput(
            type=DataType.Content,
            category=self._category or PrimitiveTypesGenerator.generate_random_enum(enum_type=ContentCategory),
            title=self._title
            or PrimitiveTypesGenerator.generate_random_string(max_length=ContentValueLimits.TITLE_MAX_LENGTH),
            url=self._url or f"https://example.com/{PrimitiveTypesGenerator.generate_random_string()}",
            rating=self._rating
            or PrimitiveTypesGenerator.generate_random_int(
                min_value=ContentValueLimits.CONTENT_RATING_MINIMUM_VALUE,
                max_value=ContentValueLimits.CONTENT_RATING_MAXIMUM_VALUE,
                allow_none=True,
            ),
            **base_fields,
        )

    def with_category(self, category: ContentCategory):
        self._category = category
        return self

    def with_title(self, title: str | None):
        self._title = title
        return self

    def with_url(self, url: str | None):
        self._url = url
        return self

    def with_rating(self, rating: int | None):
        self._rating = rating
        return self
