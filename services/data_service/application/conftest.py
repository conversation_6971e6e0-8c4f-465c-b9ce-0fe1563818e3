import pytest

from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.domain.enums.metadata import Organization, Service


@pytest.fixture
def metadata_empty_input():
    return MetadataParametersInputBoundary(
        organization=None,
        service=None,
        important=None,
        urgent=None,
        tags=None,
    )


@pytest.fixture
def metadata_partial_input():
    return MetadataParametersInputBoundary(
        organization=Organization.LLIF,
        service=None,
        important=True,
        urgent=False,
        tags=None,
    )


@pytest.fixture
def metadata_complete_input():
    return MetadataParametersInputBoundary(
        organization=Organization.LLIF,
        service=Service.DIARY,
        important=False,
        urgent=False,
        tags=["hello", "world"],
    )
