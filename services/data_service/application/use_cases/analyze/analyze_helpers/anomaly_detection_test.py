import pytest

from services.data_service.application.use_cases.analyze.analyze_helpers.anomaly_detection import (
    Anomaly,
    AnomalyDetection,
    AnomalyType,
)


@pytest.mark.parametrize(
    "data_series, x_labels, k, expected_anomalies",
    [
        pytest.param([], None, 1.5, [], id="empty_series"),
        pytest.param([10], None, 1.5, [], id="single_element"),
        pytest.param([10, 20], None, 1.5, [], id="two_elements"),
        pytest.param([5, 5, 5, 5, 5], None, 1.5, [], id="all_same_values"),
        pytest.param([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], None, 1.5, [], id="no_anomalies_sequential"),
        pytest.param(
            [10, 12, 13, 14, 15, 16, 17, 0],
            None,
            1.5,
            [Anomaly(index=7, value=0, anomaly_type=AnomalyType.DIP, description="", label=None)],
            id="single_dip_anomaly",
        ),
        pytest.param(
            [10, 12, 13, 14, 15, 16, 17, 30],
            None,
            1.5,
            [Anomaly(index=7, value=30, anomaly_type=AnomalyType.SPIKE, description="", label=None)],
            id="single_spike_anomaly",
        ),
        pytest.param(
            [0, 10, 12, 13, 14, 15, 16, 17, 30],
            None,
            1.5,
            [
                Anomaly(index=0, value=0, anomaly_type=AnomalyType.DIP, description="", label=None),
                Anomaly(index=8, value=30, anomaly_type=AnomalyType.SPIKE, description="", label=None),
            ],
            id="multiple_anomalies_no_labels",
        ),
        pytest.param(
            [0, 10, 12, 13, 14, 15, 16, 17, 30],
            ["a", "b", "c", "d", "e", "f", "g", "h", "i"],
            1.5,
            [
                Anomaly(index=0, label="a", value=0, anomaly_type=AnomalyType.DIP, description=""),
                Anomaly(index=8, label="i", value=30, anomaly_type=AnomalyType.SPIKE, description=""),
            ],
            id="multiple_anomalies_with_labels",
        ),
        pytest.param(
            [0, 10, 12, 13, 14, 15, 16, 17, 30],
            None,
            2.5,
            [
                Anomaly(index=0, value=0, anomaly_type=AnomalyType.DIP, description="", label=None),
                Anomaly(index=8, value=30, anomaly_type=AnomalyType.SPIKE, description="", label=None),
            ],
            id="different_k_value",
        ),
        pytest.param(
            [1, 1, 2, 1, 1],
            None,
            1.5,
            [Anomaly(index=2, value=2, anomaly_type=AnomalyType.SPIKE, description="", label=None)],
            id="iqr_zero_with_spike",
        ),
        pytest.param(
            [1, 1, 1, 1, 1, 1, 1, 100],
            None,
            1.5,
            [Anomaly(index=7, value=100, anomaly_type=AnomalyType.SPIKE, description="", label=None)],
            id="iqr_zero_large_spike",
        ),
    ],
)
def test_iqr_detect(data_series, x_labels, k, expected_anomalies):
    actual_anomalies = AnomalyDetection.iqr_detect(data_series, x_labels, k)

    actual_anomalies.sort(key=lambda a: (a.index, a.value))
    expected_anomalies.sort(key=lambda a: (a.index, a.value))

    assert len(actual_anomalies) == len(expected_anomalies)

    for actual, expected in zip(actual_anomalies, expected_anomalies):
        assert actual.index == expected.index
        assert actual.value == expected.value
        assert actual.anomaly_type == expected.anomaly_type
