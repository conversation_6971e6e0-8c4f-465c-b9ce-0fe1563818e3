import logging
from typing import List, Optional
from uuid import UUID

from pydantic import ValidationError

from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.application.boundaries.time_input import TimeInput
from services.base.application.exceptions import NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.heart_rate import HeartRate, HeartRateFields
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel, TimestampModel
from services.base.domain.schemas.user import User
from services.base.infrastructure.database.opensearch.query_methods.async_fetchers import (
    get_time_aggregated_fields_async,
)
from services.base.infrastructure.database.opensearch.query_methods.results_filters import (
    filter_results_from_aggregation,
)
from services.base.infrastructure.database.opensearch.query_methods.utils import (
    are_results_empty,
    get_fields_and_aggregator_as_tuple,
)
from services.data_service.application.utils.find_latest_entries import find_latest_entries
from services.data_service.application.utils.metadata_filters import prepare_metadata_query


class AggregateHeartRateUseCaseOutputItem(TimestampModel):
    bpm_avg: Optional[float] = None
    bpm_max: Optional[float] = None
    bpm_min: Optional[float] = None


class AggregateHeartRateUseCaseOutputBoundary(BaseDataModel):
    results: List[AggregateHeartRateUseCaseOutputItem]
    re_fetch_time_input: Optional[TimeInput] = None


class AggregateHeartRateUseCase:
    async def execute_async(
        self,
        user_uuid: UUID,
        time_input: TimeInput,
        metadata: MetadataParametersInputBoundary,
        re_fetch: bool = False,
        **kwargs,
    ) -> AggregateHeartRateUseCaseOutputBoundary:
        requested_fields = get_fields_and_aggregator_as_tuple(
            [
                DocumentLabels.TIMESTAMP,
                HeartRateFields.BPM_AVG,
                HeartRateFields.BPM_MIN,
                HeartRateFields.BPM_MAX,
            ]
        )

        and_query = (
            BooleanQueryBuilder()
            .add_queries(queries=prepare_metadata_query(metadata_input=metadata))
            .add_query(query=CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()
        query = Query(type_queries=[TypeQuery(domain_types=[HeartRate], query=and_query)])

        results = await get_time_aggregated_fields_async(
            query=query,
            requested_fields_and_agg=requested_fields,
            time_gte=time_input.time_gte,
            time_lte=time_input.time_lte,
            interval=time_input.interval,
        )

        if are_results_empty(results):
            if not re_fetch:
                raise NoContentException("No data available for the given time range.")

            new_time_input = TimeInput.map(
                model=find_latest_entries(
                    data_type=DataType.HeartRate,
                    current_user=User(user_uuid=user_uuid),
                    old_parameters=time_input,
                ),
                fields={"interval": time_input.interval},
            )
            result: AggregateHeartRateUseCaseOutputBoundary = await self.execute_async(
                user_uuid=user_uuid,
                time_input=new_time_input,
                metadata=metadata,
            )
            result.re_fetch_time_input = new_time_input
            return result

        filtered_results = filter_results_from_aggregation(
            in_results=results, requested_fields_and_agg=requested_fields
        )
        output_results = []
        for f_result in filtered_results:
            try:
                heart_rate = AggregateHeartRateUseCaseOutputItem(
                    bpm_avg=f_result.get(HeartRateFields.BPM_AVG, None),
                    bpm_max=f_result.get(HeartRateFields.BPM_MAX, None),
                    bpm_min=f_result.get(HeartRateFields.BPM_MIN, None),
                    timestamp=f_result[DocumentLabels.TIMESTAMP],
                )
            except (KeyError, ValueError, ValidationError):
                logging.exception(f"Can't process heart rate entry {f_result}")
                continue

            output_results.append(heart_rate)

        return AggregateHeartRateUseCaseOutputBoundary(results=output_results)
