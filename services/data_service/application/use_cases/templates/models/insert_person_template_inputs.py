from typing import Literal
from uuid import UUID

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.person import PersonCategory, PersonFields, PersonIdentifier
from services.base.domain.schemas.templates.payload.person_template_payload import PersonTemplatePayload
from services.data_service.application.use_cases.templates.models.insert_template_payload_input import (
    InsertTemplatePayloadInput,
)


class InsertPersonTemplateInput(InsertTemplatePayloadInput, PersonIdentifier):
    type: Literal[DataType.Person] = Field(alias=PersonFields.TYPE)
    category: PersonCategory = Field(alias=PersonFields.CATEGORY)
    rating: int | None = Field(
        alias=PersonFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    contact_id: UUID | None = Field(alias=PersonFields.CONTACT_ID)

    def to_domain(self) -> PersonTemplatePayload:
        return PersonTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            rating=self.rating,
            contact_id=self.contact_id,
        )
