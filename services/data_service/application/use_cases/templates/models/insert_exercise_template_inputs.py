from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.exercise.cardio import CardioCategory, CardioFields, CardioIdentifier
from services.base.domain.schemas.events.exercise.exercise import ExerciseCategory, ExerciseFields, ExerciseIdentifier
from services.base.domain.schemas.events.exercise.strength import StrengthCategory, StrengthFields, StrengthIdentifier
from services.base.domain.schemas.templates.payload.exercise_template_payloads import (
    CardioTemplatePayload,
    ExerciseTemplatePayload,
    StrengthTemplatePayload,
)
from services.data_service.application.use_cases.templates.models.insert_template_payload_input import (
    InsertTemplatePayloadInput,
)


class InsertExerciseTemplateInput(InsertTemplatePayloadInput, ExerciseIdentifier):
    type: Literal[DataType.Exercise] = Field(alias=ExerciseFields.TYPE)
    category: ExerciseCategory = Field(alias=ExerciseFields.CATEGORY)
    rating: int | None = Field(
        alias=ExerciseFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )

    def to_domain(self) -> ExerciseTemplatePayload:
        return ExerciseTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            rating=self.rating,
        )


class InsertStrengthTemplateInput(InsertTemplatePayloadInput, StrengthIdentifier):
    type: Literal[DataType.Strength] = Field(alias=StrengthFields.TYPE)
    category: StrengthCategory = Field(alias=ExerciseFields.CATEGORY)
    count: int = Field(alias=StrengthFields.COUNT, ge=0, le=1000)
    weight: RoundedFloat | None = Field(alias=StrengthFields.WEIGHT, ge=0, le=500)
    rating: int | None = Field(
        alias=StrengthFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )

    def to_domain(self) -> StrengthTemplatePayload:
        return StrengthTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            count=self.count,
            weight=self.weight,
            rating=self.rating,
        )


class InsertCardioTemplateInput(InsertTemplatePayloadInput, CardioIdentifier):
    type: Literal[DataType.Cardio] = Field(alias=CardioFields.TYPE)
    category: CardioCategory = Field(alias=ExerciseFields.CATEGORY)
    distance: RoundedFloat | None = Field(alias=CardioFields.DISTANCE, ge=0, le=250_000)
    elevation: RoundedFloat | None = Field(alias=CardioFields.ELEVATION, ge=-500, le=8848)
    rating: int | None = Field(
        alias=CardioFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )

    def to_domain(self) -> CardioTemplatePayload:
        return CardioTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            distance=self.distance,
            elevation=self.elevation,
            rating=self.rating,
        )
