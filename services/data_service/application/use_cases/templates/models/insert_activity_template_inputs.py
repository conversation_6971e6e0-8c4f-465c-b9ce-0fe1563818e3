from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.type_tree.activity_node import ActivityNode
from services.base.domain.schemas.events.activity import ActivityCategory, ActivityFields, ActivityIdentifier
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.templates.payload.activity_template_payload import ActivityTemplatePayload
from services.base.domain.type_tree.type_tree import type_tree
from services.data_service.application.use_cases.templates.models.insert_template_payload_input import (
    InsertTemplatePayloadInput,
)


class InsertActivityTemplateInput(InsertTemplatePayloadInput, ActivityIdentifier):
    type: Literal[DataType.Activity] = Field(alias=ActivityFields.TYPE)
    category: ActivityCategory = Field(alias=ActivityFields.CATEGORY)
    node: ActivityNode | None = Field(alias=ActivityFields.NODE, default=None)
    rating: int | None = Field(
        alias=ActivityFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )

    def to_domain(self) -> ActivityTemplatePayload:
        return ActivityTemplatePayload(
            type=self.type,
            category=self.category,
            node=self.node or type_tree.data_type_category_to_enum(data_type=DataType.Activity, category=self.category),
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            rating=self.rating,
        )
