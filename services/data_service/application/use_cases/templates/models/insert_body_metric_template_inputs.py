from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, RoundedFloat
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.body_metric.blood_glucose import (
    BloodGlucoseCategory,
    BloodGlucoseFields,
    BloodGlucoseIdentifier,
    BloodGlucoseSpecimenSource,
    BloodGlucoseValueLimits,
)
from services.base.domain.schemas.events.body_metric.blood_pressure import (
    BloodPressureCategory,
    BloodPressureFields,
    BloodPressureIdentifier,
    BloodPressureValueLimits,
)
from services.base.domain.schemas.events.body_metric.body_metric import (
    BodyMetricCategory,
    BodyMetricFields,
    BodyMetricIdentifier,
    BodyMetricLimits,
)
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.templates.payload.body_metric_template_payloads import (
    BloodGlucoseTemplatePayload,
    BloodPressureTemplatePayload,
    BodyMetricTemplatePayload,
)
from services.data_service.application.use_cases.templates.models.insert_template_payload_input import (
    InsertTemplatePayloadInput,
)


class InsertBodyMetricTemplateInput(InsertTemplatePayloadInput, BodyMetricIdentifier):
    type: Literal[DataType.BodyMetric] = Field(alias=BodyMetricFields.TYPE)
    category: BodyMetricCategory = Field(alias=BodyMetricFields.CATEGORY)
    value: RoundedFloat = Field(
        alias=BodyMetricFields.VALUE,
        ge=BodyMetricLimits.MINIMUM_VALUE,
        le=BodyMetricLimits.MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=BodyMetricFields.NOTE, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self) -> BodyMetricTemplatePayload:
        return BodyMetricTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            value=self.value,
        )


class InsertBloodPressureTemplateInput(InsertTemplatePayloadInput, BloodPressureIdentifier):
    type: Literal[DataType.BloodPressure] = Field(alias=BloodPressureFields.TYPE)
    category: BloodPressureCategory = Field(
        alias=BloodPressureFields.CATEGORY, default=BloodPressureCategory.BLOOD_PRESSURE
    )
    systolic: RoundedFloat = Field(
        alias=BloodPressureFields.SYSTOLIC,
        ge=BloodPressureValueLimits.MINIMUM_SYSTOLIC,
        le=BloodPressureValueLimits.MAXIMUM_SYSTOLIC,
    )
    diastolic: RoundedFloat = Field(
        alias=BloodPressureFields.DIASTOLIC,
        ge=BloodPressureValueLimits.MINIMUM_DIASTOLIC,
        le=BloodPressureValueLimits.MAXIMUM_DIASTOLIC,
    )
    note: NonEmptyStr | None = Field(
        alias=BloodPressureFields.NOTE, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self) -> BloodPressureTemplatePayload:
        return BloodPressureTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            systolic=self.systolic,
            diastolic=self.diastolic,
        )


class InsertBloodGlucoseTemplateInput(InsertTemplatePayloadInput, BloodGlucoseIdentifier):
    type: Literal[DataType.BloodGlucose] = Field(alias=BloodGlucoseFields.TYPE)
    category: BloodGlucoseCategory = Field(alias=BloodGlucoseFields.CATEGORY)
    value: RoundedFloat = Field(
        alias=BloodGlucoseFields.VALUE,
        ge=BloodGlucoseValueLimits.MINIMUM_VALUE,
        le=BloodGlucoseValueLimits.MAXIMUM_VALUE,
    )
    specimen_source: BloodGlucoseSpecimenSource = Field(
        alias=BloodGlucoseFields.SPECIMEN_SOURCE, default=BloodGlucoseSpecimenSource.UNKNOWN
    )
    note: NonEmptyStr | None = Field(
        alias=BloodGlucoseFields.NOTE, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1
    )

    def to_domain(self) -> BloodGlucoseTemplatePayload:
        return BloodGlucoseTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            value=self.value,
            specimen_source=self.specimen_source,
        )
