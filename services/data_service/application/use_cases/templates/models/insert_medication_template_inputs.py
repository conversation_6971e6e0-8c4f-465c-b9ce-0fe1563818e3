from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import Rounded6Float
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.medication.medication import (
    ConsumeUnit,
    MedicationCategory,
    MedicationFields,
    MedicationIdentifier,
    MedicationValueLimits,
    SingleDoseInformation,
)
from services.base.domain.schemas.templates.payload.medication_template_payload import (
    MedicationDetails,
    MedicationTemplatePayload,
)
from services.data_service.application.use_cases.templates.models.insert_template_payload_input import (
    InsertTemplatePayloadInput,
)


class InsertMedicationTemplateInput(InsertTemplatePayloadInput, MedicationIdentifier):
    type: Literal[DataType.Medication] = Field(alias=MedicationFields.TYPE)
    category: MedicationCategory = Field(alias=MedicationFields.CATEGORY)

    medication_details: MedicationDetails = Field(alias=MedicationFields.MEDICATION_DETAILS)
    single_dose_information: SingleDoseInformation = Field(alias=MedicationFields.SINGLE_DOSE_INFORMATION)
    consumed_amount: Rounded6Float = Field(
        alias=MedicationFields.CONSUMED_AMOUNT,
        ge=MedicationValueLimits.MIN_CONSUMED_QUANTITY,
        le=MedicationValueLimits.MAX_CONSUMED_QUANTITY,
    )
    consume_unit: VolumeUnit | WeightUnit | ConsumeUnit = Field(alias=MedicationFields.CONSUME_UNIT)

    def to_domain(self) -> MedicationTemplatePayload:
        return MedicationTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            medication_details=self.medication_details,
            single_dose_information=self.single_dose_information,
            consumed_amount=self.consumed_amount,
            consume_unit=self.consume_unit,
        )
