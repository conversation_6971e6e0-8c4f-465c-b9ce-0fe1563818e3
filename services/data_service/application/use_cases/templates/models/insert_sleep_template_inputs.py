from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.sleep_v3 import SleepV3Category, SleepV3Fields, SleepV3Identifier
from services.base.domain.schemas.templates.payload.sleep_v3_template_payload import SleepV3TemplatePayload
from services.data_service.application.use_cases.templates.models.insert_template_payload_input import (
    InsertTemplatePayloadInput,
)


class InsertSleepV3TemplateInput(InsertTemplatePayloadInput, SleepV3Identifier):
    type: Literal[DataType.SleepV3] = Field(alias=SleepV3Fields.TYPE)
    category: SleepV3Category = Field(alias=SleepV3Fields.CATEGORY)
    rating: int | None = Field(
        alias=SleepV3Fields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    deep_seconds: int | None = Field(alias=SleepV3Fields.DEEP_SECONDS)
    light_seconds: int | None = Field(alias=SleepV3Fields.LIGHT_SECONDS)
    rem_seconds: int | None = Field(alias=SleepV3Fields.REM_SECONDS)
    awake_seconds: int | None = Field(alias=SleepV3Fields.AWAKE_SECONDS)
    restless_moments: int | None = Field(alias=SleepV3Fields.RESTLESS_MOMENTS)
    provider_score: int | None = Field(alias=SleepV3Fields.PROVIDER_SCORE)
    llif_score: int | None = Field(alias=SleepV3Fields.LLIF_SCORE)

    def to_domain(self) -> SleepV3TemplatePayload:
        return SleepV3TemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            rating=self.rating,
            deep_seconds=self.deep_seconds,
            light_seconds=self.light_seconds,
            rem_seconds=self.rem_seconds,
            awake_seconds=self.awake_seconds,
            restless_moments=self.restless_moments,
            provider_score=self.provider_score,
            llif_score=self.llif_score,
        )
