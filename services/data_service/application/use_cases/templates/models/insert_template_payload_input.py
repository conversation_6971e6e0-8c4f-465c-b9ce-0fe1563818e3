from abc import ABC

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, UniqueSequenceStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import SECONDS_IN_365_DAYS
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.shared import BaseDataModel


class InsertTemplatePayloadInput(BaseDataModel, TypeIdentifier, ABC):
    name: NonEmptyStr = Field(alias=EventFields.NAME, max_length=EventValueLimits.MAX_NAME_LENGTH)
    tags: UniqueSequenceStr = Field(
        alias=DocumentLabels.TAGS,
        max_length=DocumentValueLimits.MaxTagsCount,
        default_factory=list,
    )
    duration: float | None = Field(ge=0, le=SECONDS_IN_365_DAYS)
    note: NonEmptyStr | None = Field(
        alias=EventFields.NOTE,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )
