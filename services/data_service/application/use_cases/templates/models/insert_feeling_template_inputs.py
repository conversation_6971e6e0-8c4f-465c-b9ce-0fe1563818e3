from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.feeling.emotion import EmotionCategory, EmotionFields, EmotionIdentifier
from services.base.domain.schemas.events.feeling.stress import (
    StressCategory,
    StressFields,
    StressIdentifier,
    StressValueLimits,
)
from services.base.domain.schemas.templates.payload.feeling_template_payloads import (
    EmotionTemplatePayload,
    StressTemplatePayload,
)
from services.data_service.application.use_cases.templates.models.insert_template_payload_input import (
    InsertTemplatePayloadInput,
)


class InsertEmotionTemplateInput(InsertTemplatePayloadInput, EmotionIdentifier):
    type: Literal[DataType.Emotion] = Field(alias=EmotionFields.TYPE)
    category: EmotionCategory = Field(alias=EmotionFields.CATEGORY)
    rating: int = Field(
        alias=EmotionFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )

    def to_domain(self) -> EmotionTemplatePayload:
        return EmotionTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            rating=self.rating,
        )


class InsertStressTemplateInput(InsertTemplatePayloadInput, StressIdentifier):
    type: Literal[DataType.Stress] = Field(alias=StressFields.TYPE)
    category: StressCategory = Field(alias=StressFields.CATEGORY)
    rating: int = Field(
        alias=StressFields.RATING,
        ge=StressValueLimits.STRESS_MINIMUM_VALUE,
        le=StressValueLimits.STRESS_MAXIMUM_VALUE,
    )

    def to_domain(self) -> StressTemplatePayload:
        return StressTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            rating=self.rating,
        )
