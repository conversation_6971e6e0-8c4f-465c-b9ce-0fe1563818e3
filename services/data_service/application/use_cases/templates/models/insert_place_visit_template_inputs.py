from typing import Literal
from uuid import UUID

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.place_category import PlaceCategory
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.place_visit import PlaceVisitFields, PlaceVisitIdentifier
from services.base.domain.schemas.templates.payload.place_visit_template_payload import PlaceVisitTemplatePayload
from services.data_service.application.use_cases.templates.models.insert_template_payload_input import (
    InsertTemplatePayloadInput,
)


class InsertPlaceVisitTemplateInput(InsertTemplatePayloadInput, PlaceVisitIdentifier):
    type: Literal[DataType.PlaceVisit] = Field(alias=PlaceVisitFields.TYPE)
    category: PlaceCategory = Field(alias=PlaceVisitFields.CATEGORY)
    place_id: UUID | None = Field(alias=PlaceVisitFields.PLACE_ID)
    rating: int | None = Field(
        alias=PlaceVisitFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )

    def to_domain(self) -> PlaceVisitTemplatePayload:
        return PlaceVisitTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            place_id=self.place_id,
            rating=self.rating,
        )
