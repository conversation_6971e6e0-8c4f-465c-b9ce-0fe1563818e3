from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.note import Note<PERSON><PERSON><PERSON><PERSON>, NoteFields, NoteIdentifier
from services.base.domain.schemas.templates.payload.note_template_payload import NoteTemplatePayload
from services.data_service.application.use_cases.templates.models.insert_template_payload_input import (
    InsertTemplatePayloadInput,
)


class InsertNoteTemplateInput(InsertTemplatePayloadInput, NoteIdentifier):
    type: Literal[DataType.Note] = Field(alias=NoteFields.TYPE)
    category: NoteCategory = Field(alias=NoteFields.CATEGORY)

    def to_domain(self) -> NoteTemplatePayload:
        return NoteTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
        )
