from typing import Literal, Sequence

from pydantic import Field

from services.base.domain.enums.body_location import BodyParts
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.symptom import (
    SymptomCategory,
    SymptomFields,
    SymptomIdentifier,
    SymptomValueLimits,
)
from services.base.domain.schemas.templates.payload.symptom_template_payload import SymptomTemplatePayload
from services.data_service.application.use_cases.templates.models.insert_template_payload_input import (
    InsertTemplatePayloadInput,
)


class InsertSymptomTemplateInput(InsertTemplatePayloadInput, SymptomIdentifier):
    type: Literal[DataType.Symptom] = Field(alias=SymptomFields.TYPE)
    category: SymptomCategory = Field(alias=SymptomFields.CATEGORY)
    rating: int | None = Field(
        alias=SymptomFields.RATING,
        ge=SymptomValueLimits.SYMPTOM_RATING_MINIMUM_VALUE,
        le=SymptomValueLimits.SYMPTOM_RATING_MAXIMUM_VALUE,
    )
    body_parts: Sequence[BodyParts] = Field(alias=SymptomFields.BODY_PARTS)

    def to_domain(self) -> SymptomTemplatePayload:
        return SymptomTemplatePayload(
            type=self.type,
            category=self.category,
            name=self.name,
            tags=self.tags,
            duration=self.duration,
            note=self.note,
            rating=self.rating,
            body_parts=self.body_parts,
        )
