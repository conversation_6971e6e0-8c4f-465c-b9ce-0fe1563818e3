import asyncio
import logging
from collections import ChainMap
from itertools import chain
from uuid import UUID

from pydantic import model_validator

from services.base.application.database.aggregation_service import AggregationService
from services.base.application.exceptions import IncorrectOperationException
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.enums.user_document_type import UserDocumentType
from services.base.domain.schemas.events.document_base import Document, RBACDocument
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.query.validators.field_validator import FieldValidator
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException
from services.base.domain.schemas.shared import BaseDataModel


class FrequencyDistributionUseCaseInputBoundary(BaseDataModel):
    query: Query
    field_name: NonEmptyStr
    owner_id: UUID

    @model_validator(mode="after")
    def validate_boundary(self):
        flatten_domain_types: list[type[Document]] = list(
            chain.from_iterable([q.domain_types for q in self.query.type_queries])
        )
        for dt in flatten_domain_types:
            try:
                FieldValidator.validate_fields(domain_type=dt, field_names=[self.field_name])
            except QueryValidationException:
                raise ValueError(f"field name {self.field_name} not found in type {dt.type_id()}")
        return self


class FrequencyDistributionUseCaseOutputBoundary(BaseDataModel):
    data: dict[UserDocumentType, dict[float | int | str, int]]


class FrequencyDistributionUseCase:
    def __init__(self, agg_service: AggregationService):
        self._agg_service = agg_service

    async def execute_async(
        self, input_boundary: FrequencyDistributionUseCaseInputBoundary
    ) -> FrequencyDistributionUseCaseOutputBoundary:
        domain_types = list(chain.from_iterable([q.domain_types for q in input_boundary.query.type_queries]))

        try:
            adjusted_field_name = self._agg_service.adjust_frequency_distribution_field(
                field_name=input_boundary.field_name,
                domain_types=domain_types,
            )
        except ValueError as err:
            logging.error(
                f"unable to count frequency distribution of {input_boundary.field_name} for types {domain_types}, err: {err}"
            )
            raise IncorrectOperationException(
                message="unable to count frequency distribution for the combination of field name and queries"
            )

        tasks = []
        for type_q in input_boundary.query.type_queries:
            for dt in domain_types:
                q = (
                    BooleanQueryBuilder()
                    .add_queries(
                        queries=[
                            type_q.query,
                            (
                                CommonLeafQueries.owner_id_value_query(user_uuid=input_boundary.owner_id)
                                if issubclass(dt, RBACDocument)
                                else CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=input_boundary.owner_id)
                            ),
                        ]
                    )
                    .build_and_query()
                )
                tasks.append(
                    self._do_count(
                        field_name=adjusted_field_name,
                        domain_type=dt,
                        query=Query(type_queries=[TypeQuery(query=q, domain_types=[dt])]),
                    )
                )
        try:
            results = await asyncio.gather(*tasks)
        except Exception as e:
            raise RuntimeError(f"Error occurred while gathering results: {str(e)}") from e

        return FrequencyDistributionUseCaseOutputBoundary(data=dict(ChainMap(*results)))

    async def _do_count(
        self, field_name: str, query: Query, domain_type: type[Document]
    ) -> dict[UserDocumentType, dict[float | int | str, int]]:
        count_result = await self._agg_service.frequency_distribution_by_query(
            field_name=field_name, query=query, size=100_000
        )

        output = {
            UserDocumentType(domain_type.type_id()): {
                r.aggregation_key: r.document_count
                for r in sorted(count_result, key=lambda i: i.document_count, reverse=True)
            }
        }
        return output
