import itertools
from asyncio import TaskGroup
from collections import defaultdict
from typing import Sequence
from uuid import UUID, uuid4

from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Service
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.document_base import EventMetadata, EventMetadataFields
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.domain.schemas.events.person import PersonFields
from services.base.domain.schemas.metadata import MetadataFields
from services.data_service.application.use_cases.events.event_validators import (
    ContactReferencesValidator,
    DuplicatesValidator,
    GroupReferencesValidator,
    PlanReferencesValidator,
    TemplateReferencesValidator,
)
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs
from services.data_service.application.use_cases.events.mappers.domain_event_transformer import (
    DomainEventTransformer,
)
from services.data_service.application.use_cases.events.models.insert_event_input_boundary import (
    InsertEventInputBoundary,
)


class InsertEventUseCase:
    def __init__(
        self,
        event_repo: EventRepository,
        event_mapper: DomainEventTransformer,
        template_validator: TemplateReferencesValidator,
        plan_validator: PlanReferencesValidator,
        duplicate_validator: DuplicatesValidator,
        contact_validator: ContactReferencesValidator,
        group_validator: GroupReferencesValidator,
    ):
        self._event_repo = event_repo
        self._event_transformer = event_mapper
        self._template_validator = template_validator
        self._plan_validator = plan_validator
        self._duplicate_validator = duplicate_validator
        self._contact_validator = contact_validator
        self._group_validator = group_validator

    async def execute_async(
        self, boundary: InsertEventInputBoundary, owner_id: UUID, force_strong_consistency: bool = False
    ) -> Sequence[Event]:
        input_events = boundary.documents
        # validate templates
        await self._template_validator.validate(data=input_events, owner_id=owner_id)
        # validate plans
        await self._plan_validator.validate(data=input_events, owner_id=owner_id)
        # validate contacts
        await self._contact_validator.validate(
            data=[d for d in input_events if hasattr(d, PersonFields.CONTACT_ID)], owner_id=owner_id  # pyright: ignore
        )

        # validate groups
        existing_groups = await self._group_validator.validate(
            data=[e for e in input_events if e.group_id], owner_id=owner_id
        )
        group_map: dict[UUID, EventGroup] = {g.id: g for g in existing_groups}

        submission_id = uuid4()
        events_with_submission_ids = []
        for e in input_events:
            s_id = submission_id
            if e.group_id:
                s_id = group_map[e.group_id].submission_id
            events_with_submission_ids.append((e, s_id))
        # map to domain objets
        events = await self.map_events_and_store_assets(
            owner_id=owner_id,
            metadata=EventMetadata.map(
                model=boundary.metadata,
                fields={
                    EventMetadataFields.SERVICE: Service.DATA,
                    MetadataFields.ORGANIZATION: Organization(boundary.metadata.origin.value),
                },
            ),
            events_with_submission_ids=events_with_submission_ids,
        )
        # validate duplicates
        await self._duplicate_validator.validate(documents=events)
        # store and return
        inserted_events = await self._event_repo.insert(
            events=events, force_strong_consistency=force_strong_consistency
        )

        # Update group child_ids for events that have group_id
        await self._update_group_child_ids_for_inserted_events(
            inserted_events=[e for e in inserted_events if e.group_id], groups=existing_groups
        )

        return inserted_events

    async def map_events_and_store_assets(
        self,
        events_with_submission_ids: Sequence[tuple[InsertEventInputs, UUID]],
        owner_id: UUID,
        metadata: EventMetadata,
        chunk_size: int = 10,  # process 10 documents at a time
    ) -> Sequence[Event]:
        events = []
        for i in range(0, len(events_with_submission_ids), chunk_size):
            chunk = events_with_submission_ids[i : i + chunk_size]
            async with TaskGroup() as group:
                tasks = [
                    group.create_task(
                        self._event_transformer.transform(
                            event_and_submission_id=event_with_submission_id,
                            owner_id=owner_id,
                            metadata=metadata,
                            group_id=event_with_submission_id[0].group_id,
                        )
                    )
                    for event_with_submission_id in chunk
                ]
            events.extend(itertools.chain.from_iterable([t.result() for t in tasks]))

        return events

    async def _update_group_child_ids_for_inserted_events(
        self, inserted_events: Sequence[Event], groups: Sequence[EventGroup]
    ) -> None:
        events_by_group_id: dict[UUID, list[UUID]] = defaultdict(list)
        for event in inserted_events:
            assert event.group_id
            events_by_group_id[event.group_id].append(event.id)

        if not events_by_group_id:
            return

        for group in groups:
            new_event_ids = events_by_group_id[group.id]
            group.child_ids = [*group.child_ids, *new_event_ids]

        await self._event_repo.update(events=groups)
