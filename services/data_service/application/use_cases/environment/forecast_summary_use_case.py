import logging
from asyncio import CancelledError, InvalidStateError, Task, TaskGroup
from datetime import datetime, timedelta, timezone
from typing import Optional, Sequence
from uuid import UUID

import numpy
from pydantic import Field

from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.application.boundaries.space_time_coordinates import SpaceTimeCoordinates
from services.base.application.boundaries.time_input import TimeRangeInput
from services.base.domain.schemas.environment import SpaceTimeInput
from services.base.domain.schemas.shared import BaseDataModel, CoordinatesModel, TimestampModel
from services.data_service.application.models.air_quality_bucket import AirQualityBucket
from services.data_service.application.models.pollen_bucket import PollenBucket
from services.data_service.application.models.weather_bucket import WeatherBucket
from services.data_service.application.use_cases.environment.forecast_environment_use_case import (
    ForecastEnvironmentUseCase,
)
from services.data_service.application.use_cases.list_location_use_case import (
    ListLocationUseCase,
    ListLocationUseCaseOutputBoundary,
)


class EnvironmentForecastBucket(TimestampModel):
    coordinates: CoordinatesModel
    # high impact
    pressure_change: float | None = Field(
        description="rate of change of barometric pressure in hPa/h. Stable: <= 1, Medium: < 3, High: > 3. Health impact"
    )
    tree_pollen: float | None = Field(
        ge=0, description="in grains per cubic meter of air. Low: <= 15, Moderate: <= 90, High: > 90. Health impact"
    )
    weed_pollen: float | None = Field(
        ge=0, description="in grains per cubic meter of air. Low: <= 15, Moderate: <= 90, High: > 90. Health impact"
    )
    grass_pollen: float | None = Field(
        ge=0, description="in grains per cubic meter of air. Low: <= 15, Moderate: <= 90, High: > 90. Health impact"
    )
    uv: float | None = Field(ge=0, description="uv index. Low: <= 3, Moderate: <= 7, High: > 7. Health impact")
    aqi_gb: float | None = Field(
        ge=0,
        le=1000,
        description="air quality based on US standard. Good: <= 50, Moderate: <= 100, Unhealthy > 100. Health impact",
    )
    # medium
    precipitation: float | None = Field()
    temperature: float | None = Field(description="temperature in celsius")
    wind: float | None = Field(description="wind speed in km/h")
    # low impact
    humidity: float | None = Field(ge=0, le=100, description="relative humidity")
    visibility: float | None = Field(
        description="visibility in meters",
    )


class ForecastSummaryUseCaseOutputBoundaryItem(BaseDataModel):
    buckets: Sequence[EnvironmentForecastBucket]
    user_id: UUID


class ForecastSummaryUseCaseOutputBoundary(BaseDataModel):
    forecasts: Sequence[ForecastSummaryUseCaseOutputBoundaryItem]


class ForecastSummaryUseCase:
    def __init__(
        self,
        list_location_uc: ListLocationUseCase,
        forecast_env_uc: ForecastEnvironmentUseCase,
    ):
        self._location_uc = list_location_uc
        self._forecast_env_uc = forecast_env_uc

    async def execute(
        self, user_ids: Sequence[UUID], timestamp: datetime, end_time: datetime
    ) -> ForecastSummaryUseCaseOutputBoundary:
        results: list[ForecastSummaryUseCaseOutputBoundaryItem] = []
        try:
            async with TaskGroup() as group:
                tasks: Sequence[Task] = [
                    group.create_task(
                        self._forecast_user(user_id=uid, timestamp=timestamp, end_time=end_time),
                    )
                    for uid in user_ids
                ]
        except Exception as error:
            logging.exception(f"could not await task group, err: {error}")
            return ForecastSummaryUseCaseOutputBoundary(forecasts=results)

        for task in tasks:
            try:
                task_result: tuple[UUID, Sequence[EnvironmentForecastBucket]]
                if task_result := task.result():
                    results.append(
                        ForecastSummaryUseCaseOutputBoundaryItem(user_id=task_result[0], buckets=task_result[1])
                    )
            except (CancelledError, InvalidStateError) as error:
                logging.warning(
                    f"async task for forecast warning workflow is in an invalid state or cancelled, error: {error}"
                )
            except Exception as error:
                logging.exception(f"forecast warning workflow raised an exception: {error}")

        return ForecastSummaryUseCaseOutputBoundary(forecasts=results)

    async def _forecast_user(
        self, user_id: UUID, timestamp: datetime, end_time: datetime
    ) -> tuple[UUID, Sequence[EnvironmentForecastBucket]]:
        # TODO: We can further optimize this by collecting users from the same location and handle them as a part of the initial user.
        # That allows us to use 1 LLM call to serve N users in populated areas.
        # Note: If we want to personalise each message for each user (mentioned by Jim on 02/05/2024), then this TODO does not apply.
        location: Optional[SpaceTimeCoordinates] = await self._get_latest_location(user_id=user_id)
        if not location:
            logging.info(f"no relevant location found for user: {user_id}")
            return user_id, []

        # Forecast next till next midnight
        space_time_input = SpaceTimeInput(
            lat=location.latitude,
            lon=location.longitude,
            timestamp=timestamp,
            end_time=end_time,
        )
        forecast_w = await self._forecast_env_uc.execute_async(
            space_time=space_time_input, environment_model=WeatherBucket
        )
        forecast_aq = await self._forecast_env_uc.execute_async(
            space_time=space_time_input, environment_model=AirQualityBucket
        )
        forecast_p = await self._forecast_env_uc.execute_async(
            space_time=space_time_input, environment_model=PollenBucket
        )

        pressures = [bucket.pressure for bucket in forecast_w.data if bucket.pressure]
        pressure_drop = max(abs(numpy.gradient(pressures))) if len(pressures) >= 2 else None

        forecast: list[EnvironmentForecastBucket] = []

        for aq, w, p in zip(forecast_aq.data, forecast_w.data, forecast_p.data):
            forecast.append(
                EnvironmentForecastBucket(
                    timestamp=aq.timestamp,
                    coordinates=aq.coordinates,
                    temperature=w.temperature.temperature if w.temperature else None,
                    wind=w.wind.speed if w.wind else None,
                    humidity=w.humidity,
                    uv=w.uv,
                    visibility=w.visibility,
                    precipitation=w.precipitation,
                    pressure_change=pressure_drop,
                    weed_pollen=p.weed.count if p.weed else 0,
                    tree_pollen=p.tree.count if p.tree else 0,
                    grass_pollen=p.grass.count if p.grass else 0,
                    aqi_gb=aq.aqi.us,
                )
            )
        return (user_id, forecast)

    async def _get_latest_location(
        self,
        user_id: UUID,
    ) -> Optional[SpaceTimeCoordinates]:
        """Returns the latest location information for the user in the past 24 hours"""
        time_now = datetime.now(timezone.utc)
        time_one_day_ago = time_now - timedelta(days=1)

        location: ListLocationUseCaseOutputBoundary = await self._location_uc.execute_async(
            user_uuid=user_id,
            time_input=TimeRangeInput(
                time_gte=time_one_day_ago,
                time_lte=time_now,
            ),
            metadata=MetadataParametersInputBoundary(),
        )
        if not location.results:
            return None

        last_entry = location.results[-1]
        coords = SpaceTimeCoordinates(
            timestamp=last_entry.timestamp,
            end_time=last_entry.end_time,
            duration=last_entry.duration,
            lat=(
                last_entry.end_coordinates.latitude
                if last_entry.end_coordinates
                else last_entry.start_coordinates.latitude
            ),
            lon=(
                last_entry.end_coordinates.longitude
                if last_entry.end_coordinates
                else last_entry.start_coordinates.longitude
            ),
        )
        return coords
