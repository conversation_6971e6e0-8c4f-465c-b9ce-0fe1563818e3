from typing import Sequence
from uuid import UUID

from services.base.domain.repository.extension_schema_repository import ExtensionSchemaRepository
from services.base.domain.schemas.extensions.extension_schema import ExtensionSchema


class ListExtensionSchemaUseCase:
    async def execute_async(
        self, extension_schema_repository: ExtensionSchemaRepository, extension_id: UUID
    ) -> Sequence[ExtensionSchema]:
        return await extension_schema_repository.list_extension_versions(extension_id=extension_id)
