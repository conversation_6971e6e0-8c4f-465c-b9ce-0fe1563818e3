from datetime import datetime, timezone
from unittest.mock import AsyncMock
from uuid import uuid4

import pytest

from services.base.domain.schemas.events.feeling.emotion import Emotion
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.data_service.application.use_cases.time_interval_aggregation_use_case import (
    TimeIntervalAggregationOutputBoundary,
    TimeIntervalAggregationUseCase,
    TimeIntervalAggregationUseCaseInputBoundary,
)


class MockDocument:
    def __init__(self, timestamp):
        self.timestamp = timestamp


class MockDocumentSearchResponse:
    def __init__(self, documents):
        self.documents = documents


@pytest.mark.asyncio
class TestTimeIntervalAggregationUseCase:
    @pytest.fixture(autouse=True)
    def setup_method(self):
        self.mock_search_service = AsyncMock()
        self.use_case = TimeIntervalAggregationUseCase(search_service=self.mock_search_service)

    async def test_successful_aggregation(self):
        user_uuid = uuid4()
        timestamps = [
            datetime(2023, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 5, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 15, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 25, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 30, tzinfo=timezone.utc),
        ]
        mock_documents = [MockDocument(ts) for ts in timestamps]
        self.mock_search_service.search_documents_by_query.return_value = MockDocumentSearchResponse(mock_documents)

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion], query=None)])

        result = await self.use_case.execute_async(
            TimeIntervalAggregationUseCaseInputBoundary(
                owner_id=user_uuid, query=query, num_bins=3, bin_boundaries=None
            )
        )

        assert isinstance(result, TimeIntervalAggregationOutputBoundary)
        assert len(result.results) == 3
        assert result.results[0].doc_count == 2
        assert result.results[1].doc_count == 0
        assert result.results[2].doc_count == 2
        assert result.standard_deviation is not None
        assert result.coefficient_of_variation is not None

    async def test_successful_aggregation_not_filling_null_values(self):
        user_uuid = uuid4()
        timestamps = [
            datetime(2023, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 5, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 15, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 25, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 30, tzinfo=timezone.utc),
        ]
        mock_documents = [MockDocument(ts) for ts in timestamps]
        self.mock_search_service.search_documents_by_query.return_value = MockDocumentSearchResponse(mock_documents)

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion], query=None)])

        result = await self.use_case.execute_async(
            TimeIntervalAggregationUseCaseInputBoundary(
                owner_id=user_uuid, query=query, num_bins=3, fill_null_values=False, bin_boundaries=None
            )
        )

        assert isinstance(result, TimeIntervalAggregationOutputBoundary)
        assert len(result.results) == 2
        assert result.results[0].doc_count == 2
        assert result.results[1].doc_count == 2
        assert result.standard_deviation is not None
        assert result.coefficient_of_variation is not None

    async def test_no_documents_returned(self):
        self.mock_search_service.search_documents_by_query.return_value = MockDocumentSearchResponse([])

        user_uuid = uuid4()
        query = Query(type_queries=[TypeQuery(domain_types=[Emotion], query=None)])

        result = await self.use_case.execute_async(
            TimeIntervalAggregationUseCaseInputBoundary(
                owner_id=user_uuid, query=query, num_bins=3, bin_boundaries=None
            )
        )

        assert result is None

    async def test_custom_bin_boundaries(self):
        user_uuid = uuid4()
        timestamps = [
            datetime(2023, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 10, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 25, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 35, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 2, 35, tzinfo=timezone.utc),
        ]
        mock_documents = [MockDocument(ts) for ts in timestamps]
        self.mock_search_service.search_documents_by_query.return_value = MockDocumentSearchResponse(mock_documents)

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion], query=None)])
        custom_boundaries = [0, 5, 15, 40]

        result = await self.use_case.execute_async(
            TimeIntervalAggregationUseCaseInputBoundary(
                owner_id=user_uuid,
                query=query,
                bin_boundaries=custom_boundaries,
                num_bins=None,
            )
        )

        assert result is not None
        assert len(result.results) == len(custom_boundaries) - 1
        assert result.results[0].doc_count == 1
        assert result.results[1].doc_count == 2
        assert result.results[2].doc_count == 1

    async def test_successful_aggregation_with_more_bins(self):
        user_uuid = uuid4()
        timestamps = [
            datetime(2023, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 2, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 5, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 8, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 12, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 16, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 24, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 27, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 32, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 10, 0, 38, tzinfo=timezone.utc),
        ]
        mock_documents = [MockDocument(ts) for ts in timestamps]
        self.mock_search_service.search_documents_by_query.return_value = MockDocumentSearchResponse(mock_documents)

        query = Query(type_queries=[TypeQuery(domain_types=[Emotion], query=None)])

        result = await self.use_case.execute_async(
            TimeIntervalAggregationUseCaseInputBoundary(
                owner_id=user_uuid,
                query=query,
                num_bins=6,
                bin_boundaries=None,
            )
        )

        assert isinstance(result, TimeIntervalAggregationOutputBoundary)
        assert len(result.results) == 6
        assert result.results[0].doc_count == 2
        assert result.results[1].doc_count == 3
        assert result.results[2].doc_count == 2
        assert result.results[3].doc_count == 1
        assert result.results[4].doc_count == 1
        assert result.results[5].doc_count == 1
        assert result.standard_deviation is not None
        assert result.coefficient_of_variation is not None
