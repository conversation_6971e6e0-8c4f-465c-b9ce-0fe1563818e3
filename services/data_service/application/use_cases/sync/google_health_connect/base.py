from abc import ABC, abstractmethod
from datetime import datetime
from enum import IntEnum
from typing import Sequence
from uuid import UUID

from pydantic import AwareDatetime, Field

from services.base.domain.schemas.records.record import Record as DomainRecord
from services.base.domain.schemas.shared import BaseDataModel


class DataOrigin(BaseDataModel):
    package_name: str = Field(alias="packageName")


class DeviceTypeGHC(IntEnum):
    DEVICE_TYPE_UNKNOWN = 0
    DEVICE_TYPE_WATCH = 1
    DEVICE_TYPE_PHONE = 2
    DEVICE_TYPE_SCALE = 3
    DEVICE_TYPE_RING = 4
    DEVICE_TYPE_HEAD_MOUNTED = 5
    DEVICE_TYPE_FITNESS_BAND = 6
    DEVICE_TYPE_CHEST_STRAP = 7
    DEVICE_TYPE_SMART_DISPLAY = 8


class Device(BaseDataModel):
    manufacturer: str | None
    model: str | None
    type: DeviceTypeGHC


class RecordingMethod(IntEnum):
    RECORDING_METHOD_UNKNOWN = 0
    RECORDING_METHOD_ACTIVELY_RECORDED = 1
    RECORDING_METHOD_AUTOMATICALLY_RECORDED = 2
    RECORDING_METHOD_MANUAL_ENTRY = 3


class Metadata(BaseDataModel):
    client_record_id: str | None = Field(alias="clientRecordId")
    client_record_version: int = Field(alias="clientRecordVersion")
    data_origin: str = Field(alias="dataOrigin")
    device: Device
    id: str
    last_modified_time: datetime = Field(alias="lastModifiedTime")
    recording_method: RecordingMethod = Field(alias="recordingMethod")


class Record(BaseDataModel, ABC):
    metadata: Metadata

    @abstractmethod
    def to_domain(self, owner_id: UUID) -> Sequence[DomainRecord]:
        pass


class IntervalRecord(Record, ABC):
    end_time: AwareDatetime = Field(alias="endTime")
    # end_zone_offset: timedelta = Field(alias="endZoneOffset") # difference between Raw GHC and React native lib
    start_time: AwareDatetime = Field(alias="startTime")
    # start_zone_offset: timedelta = Field(alias="startZoneOffset") # difference between Raw GHC and React native lib
