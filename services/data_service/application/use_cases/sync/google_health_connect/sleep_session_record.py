from __future__ import annotations

from datetime import datetime
from enum import IntEnum
from typing import Literal, Sequence
from uuid import UUID, uuid4

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin, Service, SourceOS, SourceService
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.events.document_base import DocumentMetadata, RBACSchema
from services.base.domain.schemas.records.sleep_record import SleepRecord, SleepRecordCategory, SleepStageType
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.sync.google_health_connect.base import IntervalRecord


class SleepSessionRecord(IntervalRecord):
    type: Literal["SleepSessionRecord"]
    notes: str | None
    stages: Sequence[Stage]
    title: str | None

    def to_domain(self, owner_id: UUID) -> Sequence[SleepRecord]:
        rbac = RBACSchema(owner_id=owner_id)
        return [stage.to_domain(device=self.metadata.device.model, rbac=rbac) for stage in self.stages]

    class Stage(BaseDataModel):
        end_time: datetime = Field(alias="endTime")
        start_time: datetime = Field(alias="startTime")
        type: StageType

        def to_domain(self, device: str | None, rbac: RBACSchema) -> SleepRecord:
            return SleepRecord(
                id=uuid4(),
                rbac=rbac,
                metadata=DocumentMetadata(
                    origin=Origin.BEST_LIFE,
                    origin_device=device,
                    source_os=SourceOS.ANDROID,
                    source_service=SourceService.GOOGLE_HEALTH_CONNECT,
                    service=Service.DATA,
                ),
                type=DataType.SleepRecord,
                timestamp=self.start_time,
                end_time=self.end_time,
                stage=self.type.to_domain(),
                category=SleepRecordCategory.OTHER,
            )

        class StageType(IntEnum):
            STAGE_TYPE_UNKNOWN = 0
            STAGE_TYPE_AWAKE = 1
            STAGE_TYPE_SLEEPING = 2
            STAGE_TYPE_AWAKE_OUT_OF_BED = 3
            STAGE_TYPE_SLEEPING_LIGHT = 4
            STAGE_TYPE_SLEEPING_DEEP = 5
            STAGE_TYPE_SLEEPING_REM = 6
            STAGE_TYPE_AWAKE_IN_BED = 7

            def to_domain(self) -> SleepStageType:
                match self:
                    case 0:
                        return SleepStageType.UNKNOWN
                    case 1:
                        return SleepStageType.AWAKE
                    case 2:
                        return SleepStageType.SLEEPING
                    case 3:
                        return SleepStageType.AWAKE_OUT_OF_BED
                    case 4:
                        return SleepStageType.N1N2
                    case 5:
                        return SleepStageType.N3
                    case 6:
                        return SleepStageType.REM
                    case 7:
                        return SleepStageType.AWAKE_IN_BED
                    case _:
                        raise ShouldNotReachHereException(f"Unknown stage type. Value: {self}")
