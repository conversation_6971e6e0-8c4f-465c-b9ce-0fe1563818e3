from typing import Literal, Sequence
from uuid import UUID, uuid4

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin, Service, SourceOS, SourceService
from services.base.domain.schemas.events.document_base import DocumentMetadata, RBACSchema
from services.base.domain.schemas.records.steps_record import StepsRecord as DomainStepsRecord
from services.base.domain.schemas.records.steps_record import StepsRecordCategory
from services.data_service.application.use_cases.sync.google_health_connect.base import IntervalRecord


class StepsRecord(IntervalRecord):
    type: Literal["StepsRecord"]
    count: int = Field(ge=1, le=1_000_000)

    def to_domain(self, owner_id: UUID) -> Sequence[DomainStepsRecord]:
        rbac = RBACSchema(owner_id=owner_id)
        return [
            DomainStepsRecord(
                id=uuid4(),
                metadata=DocumentMetadata(
                    origin=Origin.BEST_LIFE,
                    origin_device=self.metadata.device.model,
                    source_os=SourceOS.ANDROID,
                    source_service=SourceService.GOOGLE_HEALTH_CONNECT,
                    service=Service.DATA,
                ),
                timestamp=self.start_time,
                end_time=self.end_time,
                rbac=rbac,
                type=DataType.StepsRecord,
                category=StepsRecordCategory.OTHER,
                value=self.count,
            )
        ]
