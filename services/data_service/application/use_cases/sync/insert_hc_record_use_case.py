from itertools import chain
from typing import Sequence
from uuid import UUID

from services.base.application.database.duplicate_check_service import DuplicateCheckService
from services.base.domain.repository.record_repository import RecordRepository
from services.base.domain.schemas.records.record import Record
from services.data_service.application.use_cases.sync.google_health_connect.insert_hc_record_input_boundary import (
    InsertHCRecordInputBoundary,
)


class InsertHCRecordUseCase:
    def __init__(
        self,
        record_repo: RecordRepository,
        duplicate_check_service: DuplicateCheckService,
    ):
        self._record_repo = record_repo
        self._duplicate_check_service = duplicate_check_service

    async def execute_async(self, boundary: InsertHCRecordInputBoundary, owner_id: UUID) -> Sequence[Record]:
        ghc_records = boundary.records
        domain_records = list(chain.from_iterable(r.to_domain(owner_id=owner_id) for r in ghc_records))

        # duplication check
        await self._duplicate_check_service.validate_no_document_duplicates(documents=domain_records)

        # insert
        inserted_records = await self._record_repo.insert(records=domain_records)
        return inserted_records
