from typing import Sequence

from pydantic import Field

from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.type_tree.type_tree import TreeNode, type_tree


class SuggestDescendantNodesUseCaseInputBoundary(BaseDataModel):
    node: TreeNode
    max_depth: int = Field(default=0, ge=0)


class SuggestDescendantNodesUseCaseOutputBoundary(BaseDataModel):
    nodes: Sequence[str]


class SuggestDescendantNodesUseCase:
    def execute(
        self, input_boundary: SuggestDescendantNodesUseCaseInputBoundary
    ) -> SuggestDescendantNodesUseCaseOutputBoundary:
        descendants = type_tree.get_descendants(
            node=input_boundary.node, max_depth=input_boundary.max_depth, leaves_only=False
        )
        return SuggestDescendantNodesUseCaseOutputBoundary(nodes=[d.path for d in descendants])
