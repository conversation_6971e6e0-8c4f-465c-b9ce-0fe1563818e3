from typing import Sequence
from uuid import UUID

import numpy as np
from numpy import mean, std
from pydantic import Field, model_validator

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts, SortOrder
from services.base.domain.annotated_types import UniqueSequenceFloat
from services.base.domain.schemas.query.builders.common_query_adjustments import CommonQueryAdjustments
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.shared import BaseDataModel


class TimeIntervalAggregationUseCaseInputBoundary(BaseDataModel):
    query: Query
    owner_id: UUID
    num_bins: int | None = Field(ge=3)
    bin_boundaries: UniqueSequenceFloat | None = Field(min_length=3)
    fill_null_values: bool = True

    @model_validator(mode="after")
    def validate_bins_and_boundaries(self):
        if self.bin_boundaries is not None:
            if len(self.bin_boundaries) != len(set(self.bin_boundaries)):
                raise ValueError("bin_boundaries must not contain duplicate values.")

            if sorted(self.bin_boundaries) != list(self.bin_boundaries):
                raise ValueError("bin_boundaries must be sorted in ascending order.")

        if self.num_bins is None and self.bin_boundaries is None:
            raise ValueError("Either num_bins or bin_boundaries must be specified.")
        if self.num_bins is not None and self.bin_boundaries is not None:
            raise ValueError("Only one of num_bins or bin_boundaries can be specified.")

        return self


class TimeIntervalAggregate(BaseDataModel):
    """
    A single aggregate result for a time interval.
    """

    interval_key: str
    doc_count: int


class TimeIntervalAggregationOutputBoundary(BaseDataModel):
    results: Sequence[TimeIntervalAggregate]
    bin_edges: Sequence[float]
    standard_deviation: float
    coefficient_of_variation: float


class TimeIntervalAggregationUseCase:
    """
    Calculates and aggregates time differences between consecutive documents
    into a histogram.
    """

    def __init__(self, search_service: DocumentSearchService):
        self._search_service = search_service

    async def execute_async(
        self,
        input_boundary: TimeIntervalAggregationUseCaseInputBoundary,
    ) -> TimeIntervalAggregationOutputBoundary | None:
        query = CommonQueryAdjustments.add_user_uuid_to_query(
            query=input_boundary.query, user_uuid=input_boundary.owner_id
        )

        response = await self._search_service.search_documents_by_query(
            query=query, size=10000, sorts=[CommonSorts.timestamp(order=SortOrder.ASCENDING)]
        )
        documents = response.documents

        if not documents or len(documents) < 2:
            return None

        time_diffs_in_seconds = [
            (documents[i + 1].timestamp - documents[i].timestamp).total_seconds()  # pyright: ignore
            for i in range(len(documents) - 1)
        ]

        counts, bin_edges = np.histogram(
            time_diffs_in_seconds,
            bins=(
                input_boundary.bin_boundaries
                if input_boundary.bin_boundaries
                else input_boundary.num_bins  # pyright: ignore
            ),
        )

        bin_labels = [f"{bin_edges[i]:.2f}s - {bin_edges[i + 1]:.2f}s" for i in range(len(bin_edges) - 1)]

        aggregation_result = []
        for label, count in zip(bin_labels, counts):
            if input_boundary.fill_null_values or count > 0:
                aggregation_result.append(TimeIntervalAggregate(interval_key=label, doc_count=int(count)))

        values = time_diffs_in_seconds
        std_value = float(std(values)) if values else 0.0
        cv_value = float(std_value / float(mean(values))) if values and mean(values) != 0 else 0.0

        return TimeIntervalAggregationOutputBoundary(
            results=aggregation_result,
            bin_edges=list(bin_edges),
            standard_deviation=std_value,
            coefficient_of_variation=cv_value,
        )
