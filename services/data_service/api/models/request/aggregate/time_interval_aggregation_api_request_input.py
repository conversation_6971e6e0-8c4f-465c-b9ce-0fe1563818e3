from __future__ import annotations

from uuid import UUID

from fastapi import Body, Depends
from fastapi.exceptions import HTTPException
from pydantic import Field, ValidationError
from starlette import status

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.annotated_types import UniqueSequenceFloat
from services.data_service.api.queries.document_query_api import DocumentQueryAPI
from services.data_service.application.use_cases.time_interval_aggregation_use_case import (
    TimeIntervalAggregationUseCaseInputBoundary,
)


class TimeIntervalAggregationAPIRequestInput(DocumentQueryAPI):
    num_bins: int | None = Field(description="Number of bins to use for the histogram", ge=3)
    bin_boundaries: UniqueSequenceFloat | None = Field(description="Bin boundaries for the histogram", min_length=3)
    fill_null_values: bool = Field(default=True, description="Whether to fill null values with 0")

    @staticmethod
    def to_input_boundary(
        request_input: TimeIntervalAggregationAPIRequestInput = Body(...),
        owner_id: UUID = Depends(get_current_uuid),
    ) -> TimeIntervalAggregationUseCaseInputBoundary:
        try:
            return TimeIntervalAggregationUseCaseInputBoundary(
                query=request_input.to_query(),
                owner_id=owner_id,
                num_bins=request_input.num_bins,
                bin_boundaries=request_input.bin_boundaries,
                fill_null_values=request_input.fill_null_values,
            )
        except ValidationError as error:
            err = error.errors()[0]
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "detail": [{"input": None, "loc": ["body", "query"], "msg": err["msg"], "type": "value_error"}]
                },
            )
