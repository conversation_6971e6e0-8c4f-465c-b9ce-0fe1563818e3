from __future__ import annotations

from typing import Annotated

from fastapi import Query
from fastapi.exceptions import RequestValidationError
from pydantic import Field

from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.type_tree.type_tree import type_tree
from services.data_service.application.use_cases.type_tree.suggest_descendant_nodes_use_case import (
    SuggestDescendantNodesUseCaseInputBoundary,
)


class SuggestDescendantNodesAPIRequestInput(BaseDataModel):
    node: str
    max_depth: int = Field(default=0, ge=0, description="0 means no limit")

    @staticmethod
    def to_input_boundary(
        req_input: Annotated[SuggestDescendantNodesAPIRequestInput, Query()],
    ) -> SuggestDescendantNodesUseCaseInputBoundary:
        try:
            node = type_tree.get_node(path=req_input.node)
        except ValueError:
            raise RequestValidationError("invalid node")
        return SuggestDescendantNodesUseCaseInputBoundary(
            node=node,
            max_depth=req_input.max_depth,
        )
