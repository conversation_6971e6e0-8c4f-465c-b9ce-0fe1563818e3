from __future__ import annotations

from typing import Optional

from fastapi import Body, Query
from pydantic import Field

from services.base.api.request_input.sort_request_input import SortRequestInput
from services.base.application.database.models.sorts import Sort
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.type_tree.type_tree import type_tree
from services.data_service.api.queries.node_query_api import NodeQueryAPI
from services.data_service.api.serializers.continuation_token_marshaller import ContinuationTokenMarshaller
from services.data_service.application.models.feed_continuation_token import FeedContinuationToken
from services.data_service.application.use_cases.feed.document_feed_input_boundary import DocumentFeedInputBoundary


class NodeFeedAPIRequestInput(NodeQueryAPI):
    sort: SortRequestInput = Field(
        default=SortRequestInput(field_name=f"{DocumentLabels.SYSTEM_PROPERTIES}.{DocumentLabels.CREATED_AT}")
    )

    @staticmethod
    def to_input_boundary(
        request_input: NodeFeedAPIRequestInput = Body(...),
        limit: int = Query(default=100, gt=0, le=10000),
        continuation_token: Optional[NonEmptyStr] = Query(default=None, min_length=1),
    ) -> DocumentFeedInputBoundary:
        return DocumentFeedInputBoundary(
            limit=limit,
            query=request_input.to_query(type_tree=type_tree),
            sort=Sort(name=request_input.sort.field_name, order=request_input.sort.order),
            continuation_token=request_input._decode_continuation_token(continuation_token=continuation_token),
        )

    @staticmethod
    def _decode_continuation_token(continuation_token: Optional[str]) -> FeedContinuationToken | None:
        if continuation_token:
            return ContinuationTokenMarshaller.decode_feed_continuation_token(continuation_token)
        else:
            return None
