import json

from starlette import status

from services.base.api.output.annotated_api_outputs import RecordAPIOutput
from services.base.api.output.records.steps_record_api_output import StepsRecordAPIOutput
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.domain.enums.metadata_v3 import Origin, SourceService
from services.base.domain.repository.record_repository import RecordRepository
from services.base.domain.schemas.records.steps_record import StepsRecordCategory
from services.data_service.api.models.request.sync.insert_hc_record_api_request_input import (
    InsertHCRecordAPIRequestInput,
)
from services.data_service.api.tests.common_rpc_calls import _call_post_endpoint
from services.data_service.api.urls import SyncEndpointUrls
from services.data_service.application.use_cases.sync.google_health_connect.steps_record import (
    StepsRecord as GHCStepsRecord,
)


class TestSyncEndpoints:

    async def test_insert_ghc_step_records_endpoint_passes(
        self,
        user_headers_factory,
        record_repo: RecordRepository,
    ):
        # Arrange
        _, headers = await user_headers_factory()
        raw_payload = {
            "records": [
                {
                    "type": "StepsRecord",
                    "metadata": {
                        "recordingMethod": 3,
                        "device": {"model": None, "manufacturer": None, "type": 0},
                        "clientRecordVersion": 0,
                        "dataOrigin": "com.google.android.apps.fitness",
                        "clientRecordId": None,
                        "lastModifiedTime": "2025-09-05T12:25:58.553Z",
                        "id": "b236cb07-981b-4db8-98b3-b50a9775630d",
                    },
                    "count": 4444,
                    "endTime": "2025-09-04T12:25:42.201Z",
                    "startTime": "2025-09-04T11:55:42.200Z",
                },
                {
                    "type": "StepsRecord",
                    "metadata": {
                        "recordingMethod": 3,
                        "device": {"model": None, "manufacturer": None, "type": 0},
                        "clientRecordVersion": 0,
                        "dataOrigin": "com.google.android.apps.fitness",
                        "clientRecordId": None,
                        "lastModifiedTime": "2025-09-05T12:25:38.633Z",
                        "id": "647d12a6-d5d6-4d0a-8a32-66737c81cb5f",
                    },
                    "count": 2500,
                    "endTime": "2025-09-05T12:25:26.800Z",
                    "startTime": "2025-09-05T11:55:26.799Z",
                },
            ]
        }
        step_record1 = GHCStepsRecord(**raw_payload["records"][0])
        step_record2 = GHCStepsRecord(**raw_payload["records"][1])
        request_input = InsertHCRecordAPIRequestInput(records=[step_record1, step_record2])

        # Act
        response = await _call_post_endpoint(
            request_url=SyncEndpointUrls.HC,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[RecordAPIOutput](**response.json())
        docs = payload.documents
        assert docs
        assert len(docs) == 2

        for inserted_record, expected_record in zip(docs, request_input.records):
            assert isinstance(inserted_record, StepsRecordAPIOutput)
            assert isinstance(expected_record, GHCStepsRecord)
            assert inserted_record.timestamp == expected_record.start_time
            assert inserted_record.end_time == expected_record.end_time
            assert inserted_record.metadata.origin == Origin.BEST_LIFE
            assert inserted_record.metadata.source_service == SourceService.GOOGLE_HEALTH_CONNECT
            assert inserted_record.value == expected_record.count
            assert inserted_record.category == StepsRecordCategory.OTHER

        # Teardown
        await record_repo.delete_by_id(ids=[d.id for d in docs])


# TODO right now we don't support manual sleep input. This test can be reused when David provide synced SleepSessionRecord
# async def test_insert_ghc_sleep_records_endpoint_passes(
#     self,
#     user_headers_factory,
#     record_repo: RecordRepository,
# ):
#     # Arrange
#     _, headers = await user_headers_factory()
#     raw_payload = {
#         "records": [
#             {
#                 "type": "SleepSessionRecord",
#                 "endTime": "2025-09-05T12:26:00Z",
#                 "stages": [],
#                 "notes": None,
#                 "metadata": {
#                     "recordingMethod": 3,
#                     "device": {"model": None, "manufacturer": None, "type": 0},
#                     "clientRecordVersion": 1757078624821,
#                     "dataOrigin": "com.google.android.apps.fitness",
#                     "clientRecordId": "ae7341cc-0377-4360-969f-f3c48d129074",
#                     "lastModifiedTime": "2025-09-05T13:23:44.824Z",
#                     "id": "a4fbd40c-9d68-330b-a8c4-cfcdc6d25f2a"
#                 },
#                 "title": None,
#                 "startTime": "2025-09-05T04:26:00Z"
#             }
#         ]
#     }
#     sleep_session_record = SleepSessionRecord(**raw_payload["records"][0])
#     request_input = InsertGHCRecordAPIRequestInput(records=[sleep_session_record])
#
#     # Act
#     response = await _call_post_endpoint(
#         request_url=GHCRecordEndpointUrls.BASE,
#         headers=headers,
#         json=json.loads(request_input.model_dump_json(by_alias=True)),
#         retry=False,
#     )
#
#     # Assert
#     assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
#     payload = CommonDocumentsResponse[RecordAPIOutput](**response.json())
#     docs = payload.documents
#     assert docs
#     assert len(docs) == 0
#
#
#
#     # Teardown
#     await record_repo.delete_by_id(ids=[d.id for d in docs])
