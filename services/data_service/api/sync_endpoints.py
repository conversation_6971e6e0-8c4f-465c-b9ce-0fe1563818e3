from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.output.annotated_api_outputs import RecordAPIOutput
from services.base.api.output.mappers.record_api_output_mapper import RecordAP<PERSON>utputMapper
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.api.validators import validate_input_size
from services.base.application.exceptions import BadRequestException, DuplicateDocumentsFound
from services.data_service.api.constants import DataServicePrefixes, SyncRoutes
from services.data_service.api.models.request.sync.insert_hc_record_api_request_input import (
    InsertHCRecordAPIRequestInput,
)
from services.data_service.application.use_cases.sync.google_health_connect.insert_hc_record_input_boundary import (
    InsertHCRecordInputBoundary,
)
from services.data_service.application.use_cases.sync.insert_hc_record_use_case import InsertHCRecordUseCase

sync_router = APIRouter(
    prefix=f"{DataServicePrefixes.SYNC}",
    tags=["sync"],
    responses={404: {"description": "Not found"}},
    dependencies=[Depends(validate_input_size)],
)


@sync_router.post(SyncRoutes.HC)
async def health_connect(
    request_input: InsertHCRecordAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: InsertHCRecordUseCase = Injected(InsertHCRecordUseCase),
) -> CommonDocumentsResponse[RecordAPIOutput]:
    try:
        input_boundary = InsertHCRecordInputBoundary.map(model=request_input)
        records = await use_case.execute_async(boundary=input_boundary, owner_id=owner_id)
        documents_api_output = [RecordAPIOutputMapper.map(record) for record in records]
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="record duplicates found in the input payload") from err

    return CommonDocumentsResponse[RecordAPIOutput](documents=documents_api_output)
