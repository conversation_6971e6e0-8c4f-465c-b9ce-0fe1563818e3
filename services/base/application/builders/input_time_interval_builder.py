from __future__ import annotations

from datetime import datetime, timedelta
from typing import Self, Sequence

from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.input_validators.shared import InputTimeIntervalModel


class InputTimeIntervalBuilder:
    def __init__(self):
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None
        self._should_set_end_time = True

    def build(self) -> InputTimeIntervalModel:
        time_interval = CustomModelsGenerator.generate_random_time_interval()
        return InputTimeIntervalModel(
            timestamp=self._timestamp or time_interval.timestamp,
            end_time=time_interval.end_time if self._should_set_end_time else self._end_time,
        )

    def build_series(
        self, n: int | None = None, max_time_delta: timedelta | None = None
    ) -> Sequence[InputTimeIntervalModel]:
        timestamp = PrimitiveTypesGenerator.generate_random_aware_datetime()
        delta = (
            timedelta(
                seconds=int(
                    max_time_delta.total_seconds()
                    * PrimitiveTypesGenerator.generate_random_float(min_value=0.1, max_value=1)
                )
            )
            if max_time_delta
            else PrimitiveTypesGenerator.generate_random_timedelta()
        )
        return [
            self.with_time_interval(
                timestamp=timestamp + delta * i,
                end_time=timestamp + delta * (i + 1),
            ).build()
            for i in range(n or PrimitiveTypesGenerator.generate_random_int(max_value=5, min_value=1))
        ]

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        self._should_set_end_time = False
        return self

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_time_interval(self, timestamp: datetime, end_time: datetime | None) -> Self:
        self._timestamp = timestamp
        self._end_time = end_time
        self._should_set_end_time = False
        return self
