from __future__ import annotations

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.input_validators.shared import InputAsset


class InputAssetBuilder:
    def __init__(self):
        self._content: bytes | None = None
        self._name: str | None = None

    def build(self) -> InputAsset:
        return InputAsset(
            content=self._content or PrimitiveTypesGenerator.generate_random_bytes(),
            name=self._name or PrimitiveTypesGenerator.generate_random_string(),
        )

    def build_n(self, n: int | None = None) -> list[InputAsset]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_name(self, name: str) -> InputAssetBuilder:
        self._name = name
        return self

    def with_content(self, content: bytes) -> InputAssetBuilder:
        self._content = content
        return self
