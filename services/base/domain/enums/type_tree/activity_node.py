from enum import StrEnum


class ActivityNode(StrEnum):
    DOC_EVENT_ACTIVITY = "doc.event.activity"
    DOC_EVENT_ACTIVITY_BABY_CARE = "doc.event.activity.baby_care"
    DOC_EVENT_ACTIVITY_CLEAN = "doc.event.activity.clean"
    DOC_EVENT_ACTIVITY_CREATE = "doc.event.activity.create"
    DOC_EVENT_ACTIVITY_FINANCE = "doc.event.activity.finance"
    DOC_EVENT_ACTIVITY_GARDEN = "doc.event.activity.garden"
    DOC_EVENT_ACTIVITY_LAUNDRY = "doc.event.activity.laundry"
    DOC_EVENT_ACTIVITY_LAWN_MAINTENANCE = "doc.event.activity.lawn_maintenance"
    DOC_EVENT_ACTIVITY_MEAL = "doc.event.activity.meal"
    DOC_EVENT_ACTIVITY_MEAL_PREPARATION = "doc.event.activity.meal_preparation"
    DOC_EVENT_ACTIVITY_PARENT = "doc.event.activity.parent"
    DOC_EVENT_ACTIVITY_PET_CARE = "doc.event.activity.pet_care"
    DOC_EVENT_ACTIVITY_PLAN = "doc.event.activity.plan"
    DOC_EVENT_ACTIVITY_HOME_CARE = "doc.event.activity.home_care"
    DOC_EVENT_ACTIVITY_CAR_CARE = "doc.event.activity.car_care"
    DOC_EVENT_ACTIVITY_STUDY = "doc.event.activity.study"
    DOC_EVENT_ACTIVITY_TECHNOLOGY = "doc.event.activity.technology"
    DOC_EVENT_ACTIVITY_VOLUNTEER = "doc.event.activity.volunteer"
    DOC_EVENT_ACTIVITY_APP = "doc.event.activity.app"
    DOC_EVENT_ACTIVITY_APP_PRODUCTIVITY = "doc.event.activity.app.productivity"
    DOC_EVENT_ACTIVITY_APP_SOCIAL = "doc.event.activity.app.social"
    DOC_EVENT_ACTIVITY_APP_GAME = "doc.event.activity.app.game"
    DOC_EVENT_ACTIVITY_APP_FITNESS = "doc.event.activity.app.fitness"
    DOC_EVENT_ACTIVITY_APP_MUSIC = "doc.event.activity.app.music"
    DOC_EVENT_ACTIVITY_APP_EDUCATION = "doc.event.activity.app.education"
    DOC_EVENT_ACTIVITY_APP_PHOTOGRAPHY = "doc.event.activity.app.photography"
    DOC_EVENT_ACTIVITY_APP_ENTERTAINMENT = "doc.event.activity.app.entertainment"
    DOC_EVENT_ACTIVITY_APP_SHOPPING = "doc.event.activity.app.shopping"
    DOC_EVENT_ACTIVITY_APP_UTILITY = "doc.event.activity.app.utility"
    DOC_EVENT_ACTIVITY_APP_COMMUNICATION = "doc.event.activity.app.communication"
    DOC_EVENT_ACTIVITY_APP_LIFESTYLE = "doc.event.activity.app.lifestyle"
    DOC_EVENT_ACTIVITY_APP_FINANCE = "doc.event.activity.app.finance"
    DOC_EVENT_ACTIVITY_APP_TRAVEL = "doc.event.activity.app.travel"
    DOC_EVENT_ACTIVITY_APP_NEWS = "doc.event.activity.app.news"
    DOC_EVENT_ACTIVITY_APP_BUSINESS = "doc.event.activity.app.business"
    DOC_EVENT_ACTIVITY_APP_INTERACTIVE_AUGMENTED_REALITY = "doc.event.activity.app.interactive.augmented_reality"
    DOC_EVENT_ACTIVITY_APP_INTERACTIVE_QUIZ = "doc.event.activity.app.interactive.quiz"
    DOC_EVENT_ACTIVITY_APP_INTERACTIVE_VIRTUAL_REALITY = "doc.event.activity.app.interactive.virtual_reality"
    DOC_EVENT_ACTIVITY_APP_INTERACTIVE_GAME = "doc.event.activity.app.interactive.game"
    DOC_EVENT_ACTIVITY_INTERACTIVE = "doc.event.activity.interactive"
    DOC_EVENT_ACTIVITY_INTERACTIVE_BOARD_GAME = "doc.event.activity.interactive.board_game"
    DOC_EVENT_ACTIVITY_INTERACTIVE_CARD_GAME = "doc.event.activity.interactive.card_game"
    DOC_EVENT_ACTIVITY_INTERACTIVE_PUZZLE = "doc.event.activity.interactive.puzzle"
    DOC_EVENT_ACTIVITY_IDLE = "doc.event.activity.idle"
    DOC_EVENT_ACTIVITY_IDLE_DOWNTIME = "doc.event.activity.idle.downtime"
    DOC_EVENT_ACTIVITY_IDLE_NAP = "doc.event.activity.idle.nap"
    DOC_EVENT_ACTIVITY_IDLE_WAIT = "doc.event.activity.idle.wait"
    DOC_EVENT_ACTIVITY_INTIMACY = "doc.event.activity.intimacy"
    DOC_EVENT_ACTIVITY_INTIMACY_PARTNER = "doc.event.activity.intimacy.partner"
    DOC_EVENT_ACTIVITY_INTIMACY_SELF = "doc.event.activity.intimacy.self"
    DOC_EVENT_ACTIVITY_INTIMACY_SPIRITUAL = "doc.event.activity.intimacy.spiritual"
    DOC_EVENT_ACTIVITY_SERVICE = "doc.event.activity.service"
    DOC_EVENT_ACTIVITY_SERVICE_BEAUTY = "doc.event.activity.service.beauty"
    DOC_EVENT_ACTIVITY_SERVICE_DOCTOR = "doc.event.activity.service.doctor"
    DOC_EVENT_ACTIVITY_SERVICE_EDUCATION = "doc.event.activity.service.education"
    DOC_EVENT_ACTIVITY_SERVICE_FINANCE = "doc.event.activity.service.finance"
    DOC_EVENT_ACTIVITY_SERVICE_MENTAL_HEALTH = "doc.event.activity.service.mental_health"
    DOC_EVENT_ACTIVITY_SERVICE_PHYSICAL_HEALTH = "doc.event.activity.service.physical_health"
    DOC_EVENT_ACTIVITY_SERVICE_PROPERTY = "doc.event.activity.service.property"
    DOC_EVENT_ACTIVITY_SERVICE_VETERINARIAN = "doc.event.activity.service.veterinarian"
    DOC_EVENT_ACTIVITY_SOCIAL = "doc.event.activity.social"
    DOC_EVENT_ACTIVITY_SOCIAL_ASSIST = "doc.event.activity.social.assist"
    DOC_EVENT_ACTIVITY_SOCIAL_GROUP = "doc.event.activity.social.group"
    DOC_EVENT_ACTIVITY_SOCIAL_INDIVIDUAL = "doc.event.activity.social.individual"
    DOC_EVENT_ACTIVITY_SOCIAL_REMOTE = "doc.event.activity.social.remote"
    DOC_EVENT_ACTIVITY_TRAVEL_BIKE = "doc.event.activity.travel.bike"
    DOC_EVENT_ACTIVITY_TRAVEL_BOAT = "doc.event.activity.travel.boat"
    DOC_EVENT_ACTIVITY_TRAVEL_DRIVE = "doc.event.activity.travel.drive"
    DOC_EVENT_ACTIVITY_TRAVEL_FLY = "doc.event.activity.travel.fly"
    DOC_EVENT_ACTIVITY_TRAVEL_WALK = "doc.event.activity.travel.walk"
    DOC_EVENT_ACTIVITY_WORK = "doc.event.activity.work"
    DOC_EVENT_ACTIVITY_WORK_MENTOR = "doc.event.activity.work.mentor"
    DOC_EVENT_ACTIVITY_WORK_PRIVATE_MEETING = "doc.event.activity.work.private_meeting"
    DOC_EVENT_ACTIVITY_WORK_GROUP_MEETING = "doc.event.activity.work.group_meeting"
    DOC_EVENT_ACTIVITY_WORK_NETWORK = "doc.event.activity.work.network"
    DOC_EVENT_ACTIVITY_WORK_PRIMARY_WORK = "doc.event.activity.work.primary_work"
    DOC_EVENT_ACTIVITY_WORK_PROFESSIONAL_DEVELOPMENT = "doc.event.activity.work.professional_development"
    DOC_EVENT_ACTIVITY_WORK_SUPPLEMENTAL_WORK = "doc.event.activity.work.supplemental_work"
