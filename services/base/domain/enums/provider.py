from enum import StrEnum


class Provider(StrEnum):
    APPLE = "apple"
    AMAZON = "amazon"
    AMAZON_ALEXA = "amazon_alexa"
    FACEBOOK = "facebook"
    FITBIT = "fitbit"
    GOOGLE = "google"
    LLIF = "llif"
    NETFLIX = "netflix"
    THIRD_PARTY = "third_party"


class SupportedDataProviders(StrEnum):
    AMAZON = Provider.AMAZON.value
    FACEBOOK = Provider.FACEBOOK.value
    FITBIT = Provider.FITBIT.value
    GOOGLE = Provider.GOOGLE.value
    NETFLIX = Provider.NETFLIX.value
    THIRD_PARTY = Provider.THIRD_PARTY.value


class SupportedLoginProviders(StrEnum):
    GOOGLE = Provider.GOOGLE.value
    APPLE = Provider.APPLE.value


class SupportedApiProviders(StrEnum):
    GOOGLE = Provider.GOOGLE.value
    AMAZON = Provider.AMAZON.value
    FITBIT = Provider.FITBIT.value
    AMAZON_ALEXA = Provider.AMAZON_ALEXA.value
