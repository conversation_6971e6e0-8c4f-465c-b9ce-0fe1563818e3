import json

import pytest

from services.base.domain.enums.data_types import DataType
from services.base.domain.type_tree.type_tree import TypeTree
from services.base.paths import Paths


class TestTypeTree:
    TREE = TypeTree.from_json(json.loads(open(Paths.TYPE_TREE_JSON_PATH, "r").read()))

    def test_get_node(self):
        node = self.TREE.get_node("doc.event.nutrition.drink.tea")
        assert node.path == "doc.event.nutrition.drink.tea"
        assert node.name == "tea"
        assert node.parent_path == "doc.event.nutrition.drink"
        assert node.data_type == DataType.Drink
        assert node.index_pattern == "event_nutrition"

    def test_roots(self):
        assert self.TREE.roots == (self.TREE.get_node("doc"),)

    def test_get_parent_node(self):
        assert self.TREE.get_parent_node(self.TREE.get_node("doc.event.nutrition.drink.tea")) == self.TREE.get_node(
            "doc.event.nutrition.drink"
        )

    def test_get_children_nodes(self):
        children = self.TREE.get_children(self.TREE.get_node("doc.event.nutrition.drink"))
        assert self.TREE.get_node("doc.event.nutrition.drink.tea") in children

    def test_get_descendants_nodes(self):
        desc = self.TREE.get_descendants(self.TREE.get_node("doc.event.nutrition"))
        assert self.TREE.get_node("doc.event.nutrition.drink") in desc
        assert self.TREE.get_node("doc.event.nutrition.drink.tea") in desc

    def test_get_descendants_nodes_leaves_only(self):
        desc = self.TREE.get_descendants(self.TREE.get_node("doc.event.nutrition"), leaves_only=True)
        assert self.TREE.get_node("doc.event.nutrition.drink.tea") in desc
        assert self.TREE.get_node("doc.event.nutrition.drink") not in desc

    def test_get_node_not_found(self):
        with pytest.raises(ValueError, match="not found in tree"):
            self.TREE.get_node("non.existent.path")

    def test_get_descendants_max_depth_negative(self):
        node = self.TREE.get_node("doc.event.nutrition.drink")
        with pytest.raises(ValueError, match="max_depth must be >= 0"):
            self.TREE.get_descendants(node, max_depth=-1)

    def test_get_descendants_max_depth_zero(self):
        node = self.TREE.get_node("doc.event.nutrition.drink")
        all_desc = self.TREE.get_descendants(node, max_depth=0)
        assert isinstance(all_desc, tuple)
        assert len(all_desc) > 0

    def test_type_tree_init_empty_paths(self):
        with pytest.raises(ValueError, match="paths cannot be empty"):
            TypeTree(set())
