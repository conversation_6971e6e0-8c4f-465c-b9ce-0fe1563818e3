import ast
import json
from pathlib import Path
from typing import Sequence

from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.type_tree.type_tree import TypeTree
from services.base.paths import Paths


def generate_enum_ast(enum_name: str, values: Sequence[str]) -> ast.Module:
    """Generates an AST Module for a StrEnum."""
    enum_class = ast.ClassDef(
        name=enum_name,
        bases=[ast.Name(id="StrEnum", ctx=ast.Load())],
        keywords=[],
        body=[
            ast.Assign(
                targets=[ast.Name(id=value.upper().replace(".", "_"), ctx=ast.Store())],
                value=ast.Constant(value),
            )
            for value in values
        ],
        decorator_list=[],
    )
    module = ast.Module(
        body=[
            ast.ImportFrom(module="enum", names=[ast.alias(name="StrEnum")], level=0),
            enum_class,
        ],
        type_ignores=[],
    )
    return ast.fix_missing_locations(module)


def generate_enums_from_tree(tree_like: dict, output_dir: Path):
    output_dir.mkdir(parents=True, exist_ok=True)

    for type_id_path, types in TypeTree.split_type_enums(tree_like):
        if not type_id_path:
            raise ShouldNotReachHereException("Type id cannot be empty")
        types.insert(0, type_id_path)
        type_id = type_id_path.split(".")[-1]
        enum_ast = generate_enum_ast(enum_name=f"{type_id.capitalize()}Node", values=types)
        file_path = output_dir / f"{type_id}_node.py".lower()

        with file_path.open("w", encoding="utf-8") as f:
            f.write(ast.unparse(enum_ast))


if __name__ == "__main__":
    with open(Paths.TYPE_TREE_JSON_PATH, "r") as f:
        type_tree = json.loads(f.read())
    generate_enums_from_tree(tree_like=type_tree, output_dir=Paths.TYPE_TREE_ENUMS_PATH)
