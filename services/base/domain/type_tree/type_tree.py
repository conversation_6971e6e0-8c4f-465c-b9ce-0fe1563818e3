from __future__ import annotations

import json
import logging
from collections import defaultdict
from dataclasses import dataclass
from enum import StrEnum
from itertools import chain
from typing import Any, Iterable, Sequence

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.type_tree.activity_node import ActivityNode
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.events.activity import Activity
from services.base.infrastructure.database.opensearch.opensearch_mappings import NodeToIndexPatternMapping
from settings.app_config import settings


@dataclass(frozen=True, slots=True)
class TreeNode:
    _path: str
    _name: str
    _parent_path: str | None
    _data_type: DataType | None
    _category: str | None
    _index_pattern: str

    @classmethod
    def from_path(cls, path: str) -> TreeNode:
        if not path:
            raise ValueError("Path must be provided")

        split = path.split(".")

        return cls(
            _path=path,
            _name=split[-1],
            _parent_path=".".join(split[:-1]) if len(split) > 1 else None,
            _data_type=cls._get_data_type(split),
            _category=cls._get_category(split),
            _index_pattern=cls._get_index(split),
        )

    @property
    def path(self) -> str:
        """The full dot-separated path."""
        return self._path

    @property
    def name(self) -> str:
        """The last component of the path"""
        return self._name

    @property
    def category(self) -> str | None:
        return self._category

    @property
    def parent_path(self) -> str | None:
        return self._parent_path

    @property
    def data_type(self) -> DataType | None:
        return self._data_type

    @property
    def index_pattern(self) -> str:
        return self._index_pattern

    def __str__(self) -> str:
        return self._path

    def __repr__(self) -> str:
        return f"TreeNode('{self._path}')"

    def __hash__(self) -> int:
        return hash(self._path)

    def __eq__(self, other) -> bool:
        if isinstance(other, TreeNode):
            return self._path == other._path
        if isinstance(other, str):
            return self._path == other
        return NotImplemented

    @classmethod
    def _get_category(cls, split: Sequence[str]) -> str | None:
        # Walk from the end to find the rightmost category
        for i in range(len(split) - 1, -1, -1):
            s = split[i]
            if s in DataType:
                tail = split[i + 1 :]
                return ".".join(tail).replace(".", "_") if tail else None
        return None

    @staticmethod
    def _get_data_type(split: Sequence[str]) -> DataType | None:
        # Walk from the start to find the rightmost data type
        # TODO: Bug - there are colliding types and collections atm
        for s in split:
            if s in DataType:
                return DataType(s)
        return None

    @staticmethod
    def _get_index(split: Sequence[str]) -> str:
        # Walk from the end to find the rightmost index pattern
        for s in reversed(split):
            if s in NodeToIndexPatternMapping.keys():
                return NodeToIndexPatternMapping[s]
        raise ShouldNotReachHereException(f"Index not found for path: {split}")


class TypeTree:
    __slots__ = (
        "_roots",
        "_nodes",
        "_by_path",
        "_children",
        "_depth",
        "_descendants",
        "_leaf_descendants",
        "_node_to_category",
    )

    def __init__(self, paths: set[str]):
        if not paths:
            raise ValueError("paths cannot be empty")

        self._roots: tuple[TreeNode, ...]
        self._nodes: tuple[TreeNode, ...]
        self._by_path: dict[str, TreeNode]
        self._children: dict[str, tuple[TreeNode, ...]]
        self._depth: dict[str, int] = {}
        self._descendants: dict[str, tuple[TreeNode, ...]]
        self._leaf_descendants: dict[str, tuple[TreeNode, ...]]
        self._node_to_category: dict[TreeNode, str]

        self._build(paths)

    @property
    def roots(self) -> tuple[TreeNode, ...]:
        return self._roots

    def get_node(self, path: str) -> TreeNode:
        node = self._by_path.get(path)
        if not node:
            raise ValueError(f"Path '{path}' not found in tree")
        return node

    def get_parent_node(self, node: TreeNode) -> TreeNode | None:
        if not node.parent_path:
            return None
        return self._by_path.get(node.parent_path)

    def get_children(self, node: TreeNode) -> tuple[TreeNode, ...]:
        return self._children.get(node.path, ())

    def get_descendants(self, node: TreeNode, max_depth: int = 0, leaves_only: bool = False) -> tuple[TreeNode, ...]:
        if max_depth < 0:
            raise ValueError("max_depth must be >= 0")
        base = self._leaf_descendants if leaves_only else self._descendants
        desc = base.get(node.path, ())

        if max_depth == 0:
            return desc

        node_depth = self._depth[node.path]
        return tuple(n for n in desc if 1 <= (self._depth[n.path] - node_depth) <= max_depth)

    def get_node_data_types(self, node: TreeNode) -> set[DataType]:
        dts = {n.data_type for n in self.get_descendants(node) if n.data_type}
        if node.data_type:
            dts.add(node.data_type)
        return dts

    def _build(self, paths: set[str]) -> None:
        all_paths: set[str] = set()
        for path in paths:
            parts = path.split(".")
            # add all parent paths
            for i in range(1, len(parts) + 1):
                all_paths.add(".".join(parts[:i]))

        sorted_paths = sorted(all_paths)
        self._by_path: dict[str, TreeNode] = {p: TreeNode.from_path(path=p) for p in sorted_paths}
        self._nodes = tuple(self._by_path[p] for p in sorted_paths)

        children_lists: dict[str, list[TreeNode]] = defaultdict(list)
        self._depth: dict[str, int] = {}
        roots: list[TreeNode] = []

        # Build parent→children map and compute depth (number of dots in path)
        for p in sorted_paths:
            node = self._by_path[p]
            d = p.count(".")
            self._depth[p] = d
            if node.parent_path:
                children_lists[node.parent_path].append(node)
            else:
                roots.append(node)

        self._roots = tuple(roots)
        self._children = {k: tuple(v) for k, v in children_lists.items()}

        self._descendants: dict[str, tuple[TreeNode, ...]] = {}
        self._leaf_descendants: dict[str, tuple[TreeNode, ...]] = {}

        # Bottom-up: compute descendants/leaf_descendants starting from deepest nodes
        for p in reversed(sorted_paths):
            kids = self._children.get(p, ())

            if not kids:
                self._descendants[p] = ()
                self._leaf_descendants[p] = ()
                continue

            all_desc_set: set[TreeNode] = set()
            leaf_desc_set: set[TreeNode] = set()

            for ch in kids:
                all_desc_set.add(ch)
                all_desc_set.update(self._descendants[ch.path])

                ch_kids = self._children.get(ch.path, ())
                if not ch_kids:
                    leaf_desc_set.add(ch)
                else:
                    leaf_desc_set.update(self._leaf_descendants[ch.path])

            # Store descendants in deterministic order
            self._descendants[p] = tuple(sorted(all_desc_set, key=lambda n: n.path))
            self._leaf_descendants[p] = tuple(sorted(leaf_desc_set, key=lambda n: n.path))

    @classmethod
    def data_type_category_to_enum(cls, data_type: DataType, category: StrEnum):
        node_enum = {Activity.type_id(): ActivityNode}[data_type]
        cat = category.replace("_other", "").replace("_", ".").lower()
        for n in node_enum:
            if n.value.endswith(cat):
                return n

        logging.error(f"{node_enum.__name__} node not found for category {category}")
        return list(node_enum)[0]

    @classmethod
    def split_type_enums(cls, obj: dict | list, path: str = "") -> Iterable[tuple[str, list[str]]]:
        if isinstance(obj, dict):
            for k, v in obj.items():
                next_path = f"{path}.{k}" if path else k
                yield from cls.split_type_enums(v, next_path)

        elif isinstance(obj, list):
            yield path, [f"{path}.{el}" for el in obj]

        else:
            raise ShouldNotReachHereException(f"Unexpected type {type(obj)} at path '{path}'")

    @classmethod
    def from_json(cls, tree_like: dict[str, Any]) -> TypeTree:
        flat_items: list[str] = list(chain.from_iterable(prefixed for _, prefixed in cls.split_type_enums(tree_like)))
        return cls(set(flat_items))


type_tree = TypeTree.from_json(
    json.loads(open(settings.ROOT_DIR / "services/base/domain/type_tree/type_tree.json", "r").read())
)
