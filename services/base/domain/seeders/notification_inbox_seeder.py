from datetime import datetime, timezone
from typing import Sequence

from services.base.domain.enums.notification import NotificationPriority, NotificationStatus, NotificationType
from services.base.domain.schemas.inbox.member_user_notification_inbox import MemberUserNotificationInbox
from services.base.domain.seeders.seeder_base import SeederBase
from settings.app_constants import DEMO1_UUID, TEST1_UUID, TEST2_UUID, TEST3_UUID


class NotificationInboxSeeder(SeederBase):
    @staticmethod
    def seed_data() -> Sequence[MemberUserNotificationInbox]:
        return [
            MemberUserNotificationInbox(
                timestamp=datetime.now(timezone.utc),
                type=NotificationType.LIFESTYLE,
                status=NotificationStatus.UNREAD,
                priority=NotificationPriority.DEFAULT,
                title="Welcome!",
                description="Just a generic hello to you.",
                message="Welcome to Best Life!",
                user_uuid=uuid,
                # Intended to use the same uuid
                notification_uuid=uuid,
                urgent=True,
                important=True,
            )
            for uuid in (DEMO1_UUID, TEST1_UUID, TEST2_UUID, TEST3_UUID)
        ]
