from abc import ABC

from pydantic import Field

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.document_base import (
    Document,
    DocumentMetadata,
    RBACDocument,
    SystemPropertiesDocument,
    TimeIntervalDocumentStrict,
)


class RecordFields:
    METADATA = DocumentLabels.METADATA
    SUBMISSION_ID = DocumentLabels.SUBMISSION_ID
    TYPE = DocumentLabels.TYPE
    CATEGORY = DocumentLabels.CATEGORY


class Record(Document, SystemPropertiesDocument, RBACDocument, TimeIntervalDocumentStrict, ABC):
    metadata: DocumentMetadata = Field(alias=RecordFields.METADATA)
