from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import InsertableOrigin, Origin
from services.base.domain.schemas.events.medication.medication import MedicationFields, MedicationIdentifier
from services.base.domain.schemas.templates.payload.medication_template_payload import (
    Administration,
    MedicationTemplatePayload,
)
from services.base.tests.domain.builders.medication_builder import MedicationBuilder
from services.base.tests.domain.builders.template.payload.event_payload_base import EventPayloadBuilderBase


def _generate_random_medication_details():
    return {
        MedicationFields.MEDICATION_DETAILS: {
            MedicationFields.ADMINISTRATION: PrimitiveTypesGenerator.generate_random_enum(enum_type=Administration),
            MedicationFields.BRAND: PrimitiveTypesGenerator.generate_random_string(),
            MedicationFields.RX_CUID: None,
            MedicationFields.GENERIC_NAME: PrimitiveTypesGenerator.generate_random_string(),
        }
    }


class MedicationPayloadBuilder(EventPayloadBuilderBase, MedicationIdentifier):

    def build(self) -> MedicationTemplatePayload:
        return MedicationTemplatePayload.map(
            model=MedicationBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build(),
            fields=_generate_random_medication_details(),
        )
