from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import Insertable<PERSON>rigin, Origin
from services.base.domain.schemas.events.symptom import SymptomIdentifier
from services.base.domain.schemas.templates.payload.symptom_template_payload import SymptomTemplatePayload
from services.base.tests.domain.builders.symptom_builder import SymptomBuilder
from services.base.tests.domain.builders.template.payload.event_payload_base import EventPayloadBuilderBase


class SymptomPayloadBuilder(EventPayloadBuilderBase, SymptomIdentifier):

    def build(self) -> SymptomTemplatePayload:
        return SymptomTemplatePayload.map(
            model=SymptomBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )
