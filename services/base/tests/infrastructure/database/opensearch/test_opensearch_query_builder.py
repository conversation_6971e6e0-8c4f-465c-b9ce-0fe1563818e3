import pytest

from services.base.application.database.models.filter_types import (
    Ex<PERSON><PERSON><PERSON><PERSON>,
    MatchFilter,
    OrganizationTermsFilter,
    TagsTermsFilter,
    UserUUIDTermsFilter,
)
from services.base.application.database.models.filters import FilterBuilder, Filters
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.metadata import Organization
from services.base.infrastructure.database.opensearch.opensearch_query_builder import OpenSearchQueryBuilder


@pytest.mark.parametrize(
    "filter_input,expected_output",
    [
        (
            Filters(must_filters=FilterBuilder(terms_filters=[UserUUIDTermsFilter(value=["1"])])),
            {
                "bool": {
                    "filter": [
                        {
                            "term": {
                                f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}": "1",
                            }
                        }
                    ],
                }
            },
        ),
        (
            Filters(
                must_not_filters=FilterBuilder(
                    exists_filters=[
                        ExistsFilter(name=f"{DocumentLabels.SYSTEM_PROPERTIES}.{DocumentLabels.CREATED_AT}")
                    ]
                )
            ),
            {
                "bool": {
                    "must_not": [
                        {
                            "exists": {
                                "field": f"{DocumentLabels.SYSTEM_PROPERTIES}.{DocumentLabels.CREATED_AT}",
                            }
                        }
                    ]
                }
            },
        ),
        (
            Filters(
                must_filters=FilterBuilder(
                    terms_filters=[
                        OrganizationTermsFilter(value=[Organization.GOOGLE.value]),
                        TagsTermsFilter(value=["test_include", "test_include_2"]),
                    ],
                    match_filters=[
                        MatchFilter(
                            fields=["explanation", "note", "tags.tag"],
                            value="include this string",
                            exact_match=True,
                        ),
                    ],
                ),
                must_not_filters=FilterBuilder(
                    terms_filters=[
                        OrganizationTermsFilter(value=[Organization.FITBIT.value]),
                        TagsTermsFilter(value=["test_exclude", "test_exclude_2"]),
                    ]
                ),
            ),
            {
                "bool": {
                    "filter": [
                        {
                            "term": {
                                f"{DocumentLabels.METADATA}.{DocumentLabels.ORGANIZATION}": Organization.GOOGLE.value,
                            },
                        },
                        {
                            "terms": {
                                f"{DocumentLabels.TAGS}.{DocumentLabels.TAG}.keyword": [
                                    "test_include",
                                    "test_include_2",
                                ],
                            }
                        },
                    ],
                    "must_not": [
                        {
                            "term": {
                                f"{DocumentLabels.METADATA}.{DocumentLabels.ORGANIZATION}": Organization.FITBIT.value,
                            },
                        },
                        {
                            "terms": {
                                f"{DocumentLabels.TAGS}.{DocumentLabels.TAG}.keyword": [
                                    "test_exclude",
                                    "test_exclude_2",
                                ],
                            },
                        },
                    ],
                    "must": [
                        {
                            "multi_match": {
                                "fields": ["explanation", "note", "tags.tag"],
                                "query": "include this string",
                                "type": "phrase",
                            }
                        },
                    ],
                }
            },
        ),
        (
            Filters(
                must_filters=FilterBuilder(
                    match_filters=[
                        MatchFilter(
                            fields=["explanation"],
                            value="i am explaining",
                            operator="OR",
                        )
                    ]
                )
            ),
            {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "fields": ["explanation"],
                                "query": "i am explaining",
                                "analyzer": "standard",
                                "type": "cross_fields",
                                "operator": "OR",
                            }
                        }
                    ]
                }
            },
        ),
        (
            Filters(
                must_filters=FilterBuilder(
                    match_filters=[
                        MatchFilter(
                            fields=["explanation"],
                            value="i am explaining",
                            exact_match=True,
                        )
                    ]
                )
            ),
            {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "fields": ["explanation"],
                                "query": "i am explaining",
                                "type": "phrase",
                            }
                        }
                    ]
                }
            },
        ),
        (
            Filters(
                must_filters=FilterBuilder(
                    match_filters=[MatchFilter(fields=["explanation"], value="i am explaining", fuzzy_match=True)]
                )
            ),
            {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "fields": ["explanation"],
                                "query": "i am explaining",
                                "analyzer": "standard",
                                "fuzziness": "AUTO",
                                "type": "best_fields",
                            }
                        }
                    ]
                }
            },
        ),
    ],
)
def test_opensearch_query_builder_valid_filters_should_pass(
    filter_input: Filters,
    expected_output: dict,
):
    builder = OpenSearchQueryBuilder()
    query = builder.with_filters(filters=filter_input).build()

    assert query == expected_output
