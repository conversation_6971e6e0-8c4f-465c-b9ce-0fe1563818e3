"""Add extension repository

Revision ID: 076e8bfbea07
Revises: d3ccb4c1c7a9
Create Date: 2023-06-26 12:01:16.157666+00:00

"""

import sqlalchemy as sa
import sqlalchemy_utils
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "076e8bfbea07"
down_revision = "d3ccb4c1c7a9"
branch_labels = None
depends_on = None


def upgrade():
    # member user indices - should have been added long time ago
    op.create_index(op.f("ix_member_user_device_user_uuid"), "member_user_device", ["user_uuid"], unique=False)
    op.create_index(op.f("ix_member_user_user_uuid"), "member_user", ["user_uuid"], unique=False)

    # Adding missing parts from previous migration
    op.create_foreign_key(None, "member_user_device", "member_user", ["user_uuid"], ["user_uuid"], ondelete="CASCADE")
    op.alter_column(
        "member_user_device",
        "refreshed_at",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        nullable=True,
        existing_server_default=sa.text("now()"),  # pyright: ignore
    )

    op.create_table(
        "extension_provider",
        sa.Column("provider_id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), nullable=False),
        sa.Column("domain", sa.String(length=256), nullable=False),
        sa.Column("name", sa.String(length=128), nullable=False),
        sa.Column("description", sa.Text(), nullable=False),
        sa.Column("logo_url", sa.Text(), nullable=True),
        sa.Column("archived_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("provider_id"),
    )
    op.create_index(op.f("ix_extension_provider_provider_id"), "extension_provider", ["provider_id"], unique=False)

    op.create_table(
        "extension_detail",
        sa.Column("extension_id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), nullable=False),
        sa.Column("name", sa.String(length=128), nullable=True),
        sa.Column("domain", sa.String(length=128), nullable=True),
        sa.Column("description", sa.Text(), nullable=False),
        sa.Column("logo_url", sa.Text(), nullable=True),
        sa.Column("version", sa.String(length=128), nullable=False),
        sa.Column("provider_id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), nullable=False),
        sa.Column("tags", sa.JSON(), nullable=False),
        sa.Column("archived_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["provider_id"], ["extension_provider.provider_id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("extension_id"),
    )
    op.create_index(op.f("ix_extension_detail_extension_id"), "extension_detail", ["extension_id"], unique=False)
    op.create_index(op.f("ix_extension_detail_provider_id"), "extension_detail", ["provider_id"], unique=False)

    op.create_table(
        "extension_subscriptions",
        sa.Column("extension_id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), nullable=False),
        sa.Column("user_id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), nullable=False),
        sa.Column("unsubscribed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["extension_id"], ["extension_detail.extension_id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["user_id"], ["member_user.user_uuid"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("extension_id", "user_id"),
    )
    op.create_index(
        op.f("ix_extension_subscriptions_extension_id"), "extension_subscriptions", ["extension_id"], unique=False
    )
    op.create_index(op.f("ix_extension_subscriptions_user_id"), "extension_subscriptions", ["user_id"], unique=False)

    op.create_table(
        "extension_schema",
        sa.Column("extension_id", sqlalchemy_utils.types.uuid.UUIDType(binary=False), nullable=False),
        sa.Column("version", sa.String(length=32), nullable=False),
        sa.Column("input_schema", sa.JSON(), nullable=False),
        sa.Column("output_schema", sa.JSON(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(["extension_id"], ["extension_detail.extension_id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("extension_id", "version"),
    )
    op.create_index(op.f("ix_extension_schema_extension_id"), "extension_schema", ["extension_id"], unique=False)
    op.create_index(op.f("ix_extension_schema_version"), "extension_schema", ["version"], unique=False)


def downgrade():
    op.drop_index(op.f("ix_extension_schema_version"), table_name="extension_schema")
    op.drop_index(op.f("ix_extension_schema_extension_id"), table_name="extension_schema")
    op.drop_table("extension_schema")

    op.drop_index(op.f("ix_extension_subscriptions_user_id"), table_name="extension_subscriptions")
    op.drop_index(op.f("ix_extension_subscriptions_extension_id"), table_name="extension_subscriptions")
    op.drop_table("extension_subscriptions")

    op.drop_index(op.f("ix_extension_detail_provider_id"), table_name="extension_detail")
    op.drop_index(op.f("ix_extension_detail_extension_id"), table_name="extension_detail")
    op.drop_table("extension_detail")

    op.drop_index(op.f("ix_extension_provider_provider_id"), table_name="extension_provider")
    op.drop_table("extension_provider")
