"""created member_user_device table

Revision ID: d3ccb4c1c7a9
Revises: 677e3e0532da
Create Date: 2023-05-24 12:03:51.465207+00:00

"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "d3ccb4c1c7a9"
down_revision = "677e3e0532da"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "member_user_device",
        sa.Column("user_uuid", UUID(as_uuid=True), nullable=False),
        sa.PrimaryKeyConstraint("user_uuid"),
        sa.Column("device_token", sa.String(length=256), nullable=False),
        sa.Column("device_name", sa.String(length=64), nullable=True),
        sa.Column("refreshed_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=False),
        # default timestamp columns
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("member_user_device")
    # ### end Alembic commands ###
