from datetime import datetime
from uuid import UUI<PERSON>, uuid4

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, ForeignKey, String, Text
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy_utils import UUIDType

from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity


class ExtensionDetailEntity(BaseEntity):
    __tablename__ = "extension_detail"

    extension_id: Mapped[UUID] = mapped_column(
        UUIDType(binary=False),
        primary_key=True,
        default=uuid4,
        nullable=False,
        index=True,
    )

    name: Mapped[str] = mapped_column(String(length=128))
    domain: Mapped[str] = mapped_column(String(length=128))
    description: Mapped[str] = mapped_column(Text, nullable=False)
    logo_url: Mapped[str] = mapped_column(Text, nullable=True)
    version: Mapped[str] = mapped_column(String(length=32), nullable=False)
    provider_id: Mapped[UUID] = mapped_column(
        UUIDType(binary=False),
        ForeignKey("extension_provider.provider_id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    tags: Mapped[dict] = mapped_column(JSON, nullable=False)
    archived_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
