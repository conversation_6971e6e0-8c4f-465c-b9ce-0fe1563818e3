# type: ignore
# TODO: Replace usage of this file with AIQL
from __future__ import annotations

from typing import Any, Optional, Sequence

from services.base.application.database.models.filter_types import (
    Filter,
    MatchFilter,
    RangeFilter,
    TermsFilter,
)
from services.base.application.database.models.filters import Filters
from services.base.domain.constants.document_labels import DocumentLabels


class OpenSearchQueryBuilder:
    def __init__(self):
        self._query: dict[str, Any] = self._get_empty_query()

    def build(self):
        return self._query

    @staticmethod
    def _get_empty_query() -> dict:
        return {"bool": {}}

    @staticmethod
    def _filter_key_mappings(query_filter: Filter) -> str:
        return {
            DocumentLabels.ORGANIZATION: f"{DocumentLabels.METADATA}.{DocumentLabels.ORGANIZATION}",
            DocumentLabels.USER_UUID: f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}",
            DocumentLabels.SERVICE: f"{DocumentLabels.METADATA}.{DocumentLabels.SERVICE}",
            DocumentLabels.DOC_ID: f"{DocumentLabels.DOC_ID}",
            DocumentLabels.COORDINATES: f"{DocumentLabels.COORDINATES}",
            DocumentLabels.TIMESTAMP: f"{DocumentLabels.TIMESTAMP}",
            DocumentLabels.TAGS: f"{DocumentLabels.TAGS}.{DocumentLabels.TAG}.keyword",
        }.get(query_filter.name, query_filter.name)

    def with_filters(self, filters: Optional[Filters]) -> OpenSearchQueryBuilder:
        """Adds bool query clauses to the query

        Bool queries support four different clauses - `must`, `must not`, `should`, and `filter`.
        This function is a top-level entrypoint to adding these clauses to the query.

        For now, we only support `filter` and `must_not` clauses.
        """

        if not filters:
            return self

        # Filter clause
        if filters.must_filters:
            for must_filter_list, filter_parser in (
                (filters.must_filters.terms_filters, self._add_terms_filters),
                (filters.must_filters.range_filters, self._add_range_filters),
                (filters.must_filters.exists_filters, self._add_exists_filters),
            ):
                self._query = (
                    filter_parser(clause="filter", query=self._query, filters=must_filter_list)
                    if must_filter_list
                    else self._query
                )

            # Must clause
            for must_filter_list, filter_parser in ((filters.must_filters.match_filters, self._add_match_filters),):
                self._query = (
                    filter_parser(clause="must", query=self._query, filters=must_filter_list)
                    if must_filter_list
                    else self._query
                )

        # Must not clause
        if filters.must_not_filters:
            for must_not_filter_list, filter_parser in (
                (filters.must_not_filters.terms_filters, self._add_terms_filters),
                (filters.must_not_filters.range_filters, self._add_range_filters),
                (filters.must_not_filters.exists_filters, self._add_exists_filters),
            ):
                self._query = (
                    filter_parser(clause="must_not", query=self._query, filters=must_not_filter_list)
                    if must_not_filter_list
                    else self._query
                )
        return self

    @staticmethod
    def _add_range_filters(clause: str, query: dict, filters: Sequence[RangeFilter]) -> dict:
        query_filters = query["bool"].get(clause, [])

        for range_filter in filters:
            query_filters.append(
                {
                    "range": {
                        OpenSearchQueryBuilder._filter_key_mappings(range_filter): {
                            "gte": range_filter.gte,
                            "lte": range_filter.lte,
                        }
                    }
                }
            )
        query["bool"][clause] = query_filters
        return query

    @staticmethod
    def _add_terms_filters(clause: str, query: dict, filters: Sequence[TermsFilter]) -> dict:
        query_filters = query["bool"].get(clause, [])

        for term_filter in filters:
            filter_key = "term" if len(term_filter.value) == 1 else "terms"
            query_filters.append(
                {
                    filter_key: {
                        OpenSearchQueryBuilder._filter_key_mappings(term_filter): (
                            term_filter.value[0] if len(term_filter.value) == 1 else term_filter.value
                        )
                    }
                }
            )
        query["bool"][clause] = query_filters
        return query

    @staticmethod
    def _add_exists_filters(clause: str, query: dict, filters: Sequence[TermsFilter]) -> dict:
        exists_filters = query["bool"].get(clause, [])

        for exists_filter in filters:
            exists_filters.append(
                {
                    "exists": {
                        "field": OpenSearchQueryBuilder._filter_key_mappings(exists_filter),
                    }
                }
            )
        query["bool"][clause] = exists_filters
        return query

    @staticmethod
    def _add_match_filters(clause: str, query: dict, filters: Sequence[MatchFilter]) -> dict:
        query_filters = query["bool"].get(clause, [])

        for match_filter in filters:
            query_body = {
                "multi_match": {
                    "fields": match_filter.fields,
                    "query": match_filter.value,
                }
            }

            match_type = (
                "best_fields" if match_filter.fuzzy_match else "phrase" if match_filter.exact_match else "cross_fields"
            )

            query_body["multi_match"]["type"] = match_type

            if match_type != "phrase":
                query_body["multi_match"]["analyzer"] = "standard"

            # More info at [docs](https://opensearch.org/docs/latest/query-dsl/full-text/match/)
            if match_filter.operator:
                query_body["multi_match"]["operator"] = match_filter.operator

            if match_filter.fuzzy_match:
                # More info at [docs](https://opensearch.org/docs/latest/query-dsl/term/fuzzy/
                query_body["multi_match"]["fuzziness"] = "AUTO"

            query_filters.append(query_body)

        query["bool"][clause] = query_filters
        return query
