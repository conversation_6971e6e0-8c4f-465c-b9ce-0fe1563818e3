import asyncio
import logging

from services.base.infrastructure.database.opensearch.index_settings.plan import PlanIndexModel
from services.base.infrastructure.database.opensearch.opensearch_index_constants import OpenSearchIndex
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import (
    update_by_query_by_index_pattern,
)
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client

logger = logging.getLogger()
logger.setLevel(logging.INFO)


async def migrate(index_model: OpenSearchIndex, dry_run: bool):
    client = get_async_default_os_client()

    q = {"bool": {"filter": [{"exists": {"field": "streak.total_triggered"}}]}}
    count_total = await client.count(index=index_model.name)
    count_migratable = await client.count(index=index_model.name, body={"query": q})

    if dry_run:
        logger.info(f"[DRY_RUN] Would update {count_migratable['count']} documents for {index_model.name}")
        return

    query = {
        "script": {
            "source": """
                    ctx._source.total_completed_on_time = ctx._source.streak.total_triggered;
                    ctx._source.streak.remove('total_triggered');
                """,
            "lang": "painless",
        },
        "query": q,
    }

    await update_by_query_by_index_pattern(client=client, index_pattern=index_model.name, query=query)

    new_count_total = await client.count(index=index_model.name)
    new_count_migratable = await client.count(index=index_model.name, body={"query": q})
    logger.info(f"migrated {new_count_migratable['count']} documents\n-------------")
    logger.info(f"COUNT_DIFF: {count_total['count'] - new_count_total['count']}")


if __name__ == "__main__":
    asyncio.run(migrate(index_model=PlanIndexModel, dry_run=False))
