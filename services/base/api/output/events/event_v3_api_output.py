from typing import Literal, Sequence
from uuid import UUID

from pydantic import Field

from services.base.api.output.events.event_api_output_base import EventAPIOutputBase
from services.base.domain.annotated_types import UniqueSequenceUUID
from services.base.domain.enums.body_location import BodyParts
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.place_category import PlaceCategory
from services.base.domain.enums.type_tree.activity_node import ActivityNode
from services.base.domain.schemas.events.activity import ActivityCategory, ActivityFields, ActivityIdentifier
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.event_group import EventGroupCategory, EventGroupFields, EventGroupIdentifier
from services.base.domain.schemas.events.note import NoteCategory, NoteFields, NoteIdentifier
from services.base.domain.schemas.events.person import PersonCategory, PersonFields, PersonIdentifier
from services.base.domain.schemas.events.place_visit import (
    PlaceVisitFields,
    PlaceVisitIdentifier,
)
from services.base.domain.schemas.events.symptom import (
    SymptomCategory,
    SymptomFields,
    SymptomIdentifier,
    SymptomValueLimits,
)


class ActivityAPIOutput(EventAPIOutputBase, ActivityIdentifier):
    """API output model for activity data"""

    type: Literal[DataType.Activity] = Field(
        alias=ActivityFields.TYPE, description="The type indicating it's an activity"
    )
    category: ActivityCategory = Field(alias=ActivityFields.CATEGORY)
    node: ActivityNode = Field(alias=ActivityFields.NODE)
    rating: int | None = Field(
        alias=ActivityFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )


class NoteAPIOutput(EventAPIOutputBase, NoteIdentifier):
    """API output model for note data"""

    type: Literal[DataType.Note] = Field(alias=NoteFields.TYPE, description="The type indicating it's a note")
    category: NoteCategory = Field(alias=NoteFields.CATEGORY)


class SymptomAPIOutput(EventAPIOutputBase, SymptomIdentifier):
    """API output model for symptom data"""

    type: Literal[DataType.Symptom] = Field(alias=SymptomFields.TYPE, description="The type indicating it's a symptom")
    category: SymptomCategory = Field(alias=SymptomFields.CATEGORY)
    rating: int | None = Field(
        alias=SymptomFields.RATING,
        ge=SymptomValueLimits.SYMPTOM_RATING_MINIMUM_VALUE,
        le=SymptomValueLimits.SYMPTOM_RATING_MAXIMUM_VALUE,
    )
    body_parts: Sequence[BodyParts] = Field(alias=SymptomFields.BODY_PARTS)


class EventGroupAPIOutput(EventAPIOutputBase, EventGroupIdentifier):
    """API output model for event group data"""

    category: EventGroupCategory = Field(alias=EventGroupFields.CATEGORY)
    type: Literal[DataType.EventGroup] = Field(
        alias=EventGroupFields.TYPE, description="The type indicating it's an event group"
    )
    child_ids: UniqueSequenceUUID = Field(alias=EventGroupFields.CHILD_IDS)


class PersonAPIOutput(EventAPIOutputBase, PersonIdentifier):
    """API output model for person data"""

    type: Literal[DataType.Person] = Field(alias=PersonFields.TYPE, description="The type indicating it's a person")
    category: PersonCategory = Field(alias=PersonFields.CATEGORY, description="The category of the person")
    rating: int | None = Field(
        alias=PersonFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
        description="The rating of the event which happened with the person",
    )
    contact_id: UUID | None = Field(alias=PersonFields.CONTACT_ID, description="The contact id of the person")


class PlaceVisitAPIOutput(EventAPIOutputBase, PlaceVisitIdentifier):
    """API output model for place visit data"""

    type: Literal[DataType.PlaceVisit] = Field(
        alias=PlaceVisitFields.TYPE, description="The type indicating it's a place visit"
    )
    category: PlaceCategory = Field(
        alias=PlaceVisitFields.CATEGORY, description="The category of the place (home, work, etc.)"
    )
    place_id: UUID | None = Field(alias=PlaceVisitFields.PLACE_ID, description="The place id of the place visit")
    rating: int | None = Field(
        alias=PlaceVisitFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
        description="The rating of the event which happened at the place",
    )
