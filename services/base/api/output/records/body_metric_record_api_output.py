from typing import Literal

from pydantic import Field

from services.base.api.output.events.event_metadata_api_output import EventMetadataAPIOutput
from services.base.api.output.shared.identifiable_document_api_output import (
    IdentifiableDocumentAPIOutput,
)
from services.base.api.output.shared.system_properties_document_api_output import (
    SystemPropertiesDocumentAPIOutput,
)
from services.base.api.output.shared.time_interval_document_api_output import (
    TimeIntervalDocumentStrictAPIOutput,
)
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.records.body_metric_record import (
    BodyMetricRecordCategory,
    BodyMetricRecordFields,
    BodyMetricRecordIdentifier,
)


class BodyMetricRecordAPIOutput(
    BodyMetricRecordIdentifier,
    SystemPropertiesDocumentAPIOutput,
    IdentifiableDocumentAPIOutput,
    TimeIntervalDocumentStrictAPIOutput,
):
    type: Literal[DataType.BodyMetricRecord] = Field(alias=BodyMetricRecordFields.TYPE)
    category: BodyMetricRecordCategory = Field(alias=BodyMetricRecordFields.CATEGORY)
    value: float = Field(alias=BodyMetricRecordFields.VALUE)
    metadata: EventMetadataAPIOutput
