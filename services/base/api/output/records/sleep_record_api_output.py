from typing import Literal

from pydantic import Field

from services.base.api.output.events.event_metadata_api_output import EventMetadataAPIOutput
from services.base.api.output.shared.identifiable_document_api_output import (
    IdentifiableDocumentAPIOutput,
)
from services.base.api.output.shared.system_properties_document_api_output import (
    SystemPropertiesDocumentAPIOutput,
)
from services.base.api.output.shared.time_interval_document_api_output import (
    TimeIntervalDocumentStrictAPIOutput,
)
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.records.sleep_record import (
    SleepRecordCategory,
    SleepRecordFields,
    SleepRecordIdentifier,
    SleepStageType,
)


class SleepRecordAPIOutput(
    SleepRecordIdentifier,
    SystemPropertiesDocumentAPIOutput,
    IdentifiableDocumentAPIOutput,
    TimeIntervalDocumentStrictAPIOutput,
):
    type: Literal[DataType.SleepRecord] = Field(alias=SleepRecordFields.TYPE)
    category: SleepRecordCategory = Field(alias=SleepRecordFields.CATEGORY)
    stage: SleepStageType = Field(alias=SleepRecordFields.STAGE)
    metadata: EventMetadataAPIOutput
