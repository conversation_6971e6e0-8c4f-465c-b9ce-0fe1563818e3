import random
import string

_VOICE_TEST_WORDS = [
    "Vacation",
    "Anniversary",
    "Tandoori chicken",
    "Appointment",
    "Nuts",
    "Celebration",
    "Plant",
    "mucinex decongestant nasal spray",
    "turkey stick",
]


class VoiceTestHelpers:
    @staticmethod
    def malform_word(word: str):
        operations = ["delete", "replace", "insert", "plural"]
        operation = random.choice(operations)
        pos = random.randint(0, len(word) - 1)
        char = random.choice(string.ascii_letters)

        if operation == "delete" and len(word) > 1:
            return word[:pos] + word[pos + 1 :]
        elif operation == "replace":
            return word[:pos] + char + word[pos + 1 :]
        elif operation == "insert":
            return word[:pos] + char + word[pos:]
        elif operation == "plural":
            return word + "s"

        return word
