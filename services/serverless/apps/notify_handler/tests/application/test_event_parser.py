from datetime import datetime, timezone
from uuid import UUID

import pytest
from dateutil import parser

from services.base.application.event_models.analytics_completed_event_model import AnalyticsCompletedEventModel
from services.base.application.event_models.takeout_export_finished_event_model import TakeoutExportFinishedModel
from services.base.domain.constants.messaging import MessageTopics
from services.serverless.apps.notify_handler.event_parser import (
    get_model_from_sns_attribute,
    parse_event_input_to_models,
)
from services.serverless.apps.trend_insights.app.constants.messages import TrendInsightsResultMessages
from services.serverless.base.parsers.parse_lambda_triggers import MessagePayload
from settings.extension_constants import TREND_INSIGHTS_EXTENSION_ID


def test_parse_event_input_to_takeout_model_should_pass():
    # Arrange
    expected_output = TakeoutExportFinishedModel(
        user_uuid=UUID("9e83c3a9-792f-4bba-9839-09d92cb24b54"),
        timestamp=parser.parse("2022-11-30T12:25:23+0000"),
        takeout_name=f"{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}-export",
    )
    payload = MessagePayload(
        message_attributes={
            "user_event": {
                "Type": "String",
                "Value": MessageTopics.TOPIC_TAKEOUT_EXPORT_FINISHED.value,
            }
        },
        payload=expected_output.model_dump(),
    )

    # Act
    model = parse_event_input_to_models(payloads=[payload])[0]

    # Assert
    assert isinstance(model, TakeoutExportFinishedModel)
    assert model == expected_output


def test_parse_event_input_to_analytic_model_should_pass():
    # Arrange
    expected_output = AnalyticsCompletedEventModel(
        doc_id=UUID("9e83c3a9-792f-4bba-9839-09d92cb24b54"),
        user_uuid=UUID("9e83c3a9-792f-4bba-9839-09d92cb24b54"),
        timestamp=parser.parse("2022-11-30T12:25:23+0000"),
        extension_id=TREND_INSIGHTS_EXTENSION_ID,
        title=TrendInsightsResultMessages.RUN_WITH_OUTPUT_TITLE,
    )
    payload = MessagePayload(
        message_attributes={
            "user_event": {
                "Type": "String",
                "Value": MessageTopics.TOPIC_ANALYTIC_FINISHED.value,
            }
        },
        payload=expected_output.model_dump(),
    )

    # Act
    model = parse_event_input_to_models(payloads=[payload])[0]

    # Assert
    assert isinstance(model, AnalyticsCompletedEventModel)
    assert model == expected_output


@pytest.mark.parametrize(
    "message_attributes,expected_output",
    [
        (
            {
                "user_event": {
                    "Type": "String",
                    "Value": MessageTopics.TOPIC_ANALYTIC_FINISHED.value,
                }
            },
            AnalyticsCompletedEventModel,
        ),
        (
            {
                "user_event": {
                    "Type": "String",
                    "Value": MessageTopics.TOPIC_TAKEOUT_EXPORT_FINISHED.value,
                }
            },
            TakeoutExportFinishedModel,
        ),
    ],
)
def test_get_model_from_sns_attribute_should_pass(message_attributes, expected_output):
    # Act
    model = get_model_from_sns_attribute(attr=message_attributes)

    # Validate
    assert model == expected_output
