from services.base.domain.enums.data_types import DataType
from services.file_service.application.loaders.fitbit_loaders.file_loaders.sleep_detail_loader import (
    SleepDetailLoader,
)
from services.file_service.tests.application.loaders.conftest import (
    TEST_DATA_SOURCE_DIR_PATH,
    TEST_UUID,
    basic_loader_test,
)
from settings.app_constants import MESSAGE_NO_FILES_FOUND, PATH_WHICH_DOES_NOT_EXIST


async def test_sleep_detail_loader_load_files_data(os_client_mock):
    # Arrange
    sleep_detail_loader = SleepDetailLoader(
        user_uuid=TEST_UUID,
        data_dir_path=TEST_DATA_SOURCE_DIR_PATH,
        data_type=DataType.Sleep,
        client=os_client_mock,
    )

    # Act & Assert
    await basic_loader_test(loader=sleep_detail_loader, processing_method=sleep_detail_loader.load_files_data)


def test_sleep_detail_loader_missing_file(caplog, os_client_mock):
    # Arrange
    sleep_detail_loader = SleepDetailLoader(
        user_uuid=TEST_UUID,
        data_dir_path=TEST_DATA_SOURCE_DIR_PATH,
        data_type=DataType.Sleep,
        client=os_client_mock,
    )
    sleep_detail_loader.search_path = PATH_WHICH_DOES_NOT_EXIST
    # Act
    sleep_detail_loader.load_files_data()
    # Assert
    assert MESSAGE_NO_FILES_FOUND in caplog.text
