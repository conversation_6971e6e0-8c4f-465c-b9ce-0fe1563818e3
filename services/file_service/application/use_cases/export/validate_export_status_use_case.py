import logging
from datetime import datetime, timedelta, timezone

from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.task_status import TaskStatus
from services.base.domain.repository.export_task_repository import ExportTaskRepository
from services.base.domain.repository.wrappers import Range, ReadFromDatabaseWrapper
from services.base.domain.schemas.export_task import ExportTaskSchemaFields


class ValidateIdleExportTasksUseCase(AsyncUseCaseBase):
    def __init__(self, export_repo: ExportTaskRepository):
        self._export_task_repo = export_repo

    async def execute_async(self, timedelta_considered_idle: timedelta):
        now = datetime.now(timezone.utc)
        wrapper = ReadFromDatabaseWrapper(
            search_keys={ExportTaskSchemaFields.STATUS: TaskStatus.IN_PROGRESS.value},
            range_filter=Range(field_name=DocumentLabels.UPDATED_AT, end_date=now - timedelta_considered_idle),
        )
        tasks = await self._export_task_repo.get(wrapper=wrapper)
        for task in tasks:
            logging.error(f"Found idle task: {task}, marking as failed.")
            task.status = TaskStatus.FAILED
            _ = await self._export_task_repo.insert_or_update(task=task)
