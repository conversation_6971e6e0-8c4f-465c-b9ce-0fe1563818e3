import logging
from typing import Sequence, <PERSON><PERSON>
from zoneinfo import ZoneInfo

from opensearchpy import Async<PERSON>penSearch

from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.application.boundaries.space_time_coordinates import SpaceTimeCoordinates
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.environment import Environment
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import RadiusQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.infrastructure.database.opensearch.query_methods.async_fetchers import get_data_by_multi_search_async
from services.base.infrastructure.database.opensearch.query_methods.data_fetchers import OpenSearchWrapper
from services.base.infrastructure.database.opensearch.query_methods.results_filters import (
    filter_results_from_aggregation,
)
from services.base.infrastructure.database.opensearch.query_methods.utils import are_results_empty
from services.base.infrastructure.database.opensearch.query_translator.query_translator import QueryTranslator
from settings.app_config import settings


class SearchEnvironmentUseCase(AsyncUseCaseBase):
    @staticmethod
    async def execute_async(
        client: AsyncOpenSearch,
        space_time_data: Sequence[SpaceTimeCoordinates],
        requested_fields: Sequence[str | Tuple[str, str]],
        domain_type: type[Environment],
        aggregation_interval: str,
        timezone: ZoneInfo,
    ) -> Sequence[tuple[SpaceTimeCoordinates, Sequence[dict]]]:
        m_search_request = []
        metadata = {}
        for space_time in space_time_data:
            and_query = (
                BooleanQueryBuilder()
                .add_query(CommonLeafQueries.timestamp_range_query(lte=space_time.end_time, gte=space_time.timestamp))
                .add_query(
                    RadiusQuery(
                        field_name=DocumentLabels.COORDINATES,
                        radius=settings.ENVIRONMENT_VALID_RADIUS,
                        latitude=space_time.latitude,
                        longitude=space_time.longitude,
                    )
                )
            ).build_and_query()
            query = Query(type_queries=[TypeQuery(domain_types=[domain_type], query=and_query)])
            query_result = QueryTranslator.translate(query)

            aggs = OpenSearchWrapper.build_aggregation_query(
                requested_fields_and_agg=requested_fields,
                time_gte=space_time.timestamp,
                time_lte=space_time.end_time,
                interval=aggregation_interval,
                timezone=timezone,
                min_doc_count=1,
            )

            req_body = {**query_result.query_as_dict, "aggregations": aggs, "size": 1}
            m_search_request.extend([metadata, req_body])

        responses = []
        try:
            responses = await get_data_by_multi_search_async(
                client=client, search_body=m_search_request, domain_type=domain_type
            )
        except Exception as error:
            logging.exception(
                f"Failed to do msearch for body {m_search_request} for data type {domain_type}, err: {error}"
            )

        results = []

        for space_time, response in zip(space_time_data, responses):
            if are_results_empty(response):
                continue
            try:
                results.append(
                    (
                        space_time,
                        filter_results_from_aggregation(
                            in_results=response,
                            requested_fields_and_agg=requested_fields,
                        ),
                    )
                )
            except Exception as error:
                logging.exception(
                    f"Can't process response of space time: {space_time}, response: {response}, err: {error}"
                )
        return results
