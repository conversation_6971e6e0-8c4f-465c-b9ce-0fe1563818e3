from services.base.domain.schemas.events.feeling.emotion import EmotionFields
from services.base.domain.schemas.events.feeling.stress import StressFields
from services.file_service.application.enums.exportable_data_type import ExportableType
from services.file_service.application.mappings.csv_mappings.v3_documents.v3_non_collection_mappings import (
    V3CSVEventBaseMapping,
)

FeelingsCollectionCSVMappings = {
    ExportableType.Emotion: [
        *V3CSVEventBaseMapping,
        # Feeling fields
        EmotionFields.RATING,
        EmotionFields.NOTE,
    ],
    ExportableType.Stress: [
        *V3CSVEventBaseMapping,
        # Feeling fields
        StressFields.RATING,
        StressFields.NOTE,
    ],
}
