from datetime import datetime, timezone

from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.application.event_models.member_user_registered_event_model import MemberUserRegisteredMessageModel
from services.base.application.recovery_encryption import encrypt
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.constants.messaging import ATT_NAME_USER_EVENT, MessageTopics
from services.base.domain.enums.member_user_role import MemberUserType
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.member_user.member_user_settings import MemberUserSettings
from services.base.domain.schemas.shared import BaseDataModel
from services.base.message_queue.utils import create_string_message_attribute
from services.user_service.application.boundaries.anonymous_recovery_token import AnonymousR<PERSON>overyToken


class CreateAnonymousUserUseCaseOutputBoundary(BaseDataModel):
    user: MemberUser
    settings: MemberUserSettings
    recovery_token: NonEmptyStr


class CreateAnonymousUserUseCase(AsyncUseCaseBase):
    def __init__(
        self,
        member_user_repository: MemberUserRepository,
        member_user_settings_repository: MemberUserSettingsRepository,
        message_broker_client: AsyncMessageBrokerClient,
    ):
        self._member_user_repository = member_user_repository
        self._member_user_settings_repository = member_user_settings_repository
        self._message_broker_client = message_broker_client

    async def execute_async(
        self,
    ) -> CreateAnonymousUserUseCaseOutputBoundary:
        now = datetime.now(timezone.utc)
        anonymous_user = MemberUser(last_logged_at=now, created_at=now, type=MemberUserType.ANONYMOUS)
        await self._member_user_repository.insert_or_update(user=anonymous_user)

        user_settings = MemberUserSettings(user_uuid=anonymous_user.user_uuid)
        settings = await self._member_user_settings_repository.insert_or_update(settings=user_settings)

        await self._message_broker_client.publish_topic(
            topic_name=MessageTopics.TOPIC_MEMBER_USER_REGISTERED.value,
            message_body=MemberUserRegisteredMessageModel(
                user_uuid=anonymous_user.user_uuid, timestamp=now
            ).model_dump_json(),
            message_attributes=create_string_message_attribute(
                ATT_NAME_USER_EVENT, MessageTopics.TOPIC_MEMBER_USER_REGISTERED.value
            ),
        )

        recovery_token = AnonymousRecoveryToken(iat=datetime.now(timezone.utc), user_id=anonymous_user.user_uuid)
        return CreateAnonymousUserUseCaseOutputBoundary(
            user=anonymous_user, settings=settings, recovery_token=encrypt(string=recovery_token.model_dump_json())
        )
