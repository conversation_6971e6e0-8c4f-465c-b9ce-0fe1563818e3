.DEFAULT_GOAL := help
.EXPORT_ALL_VARIABLES:
DOCKER_NETWORK := stack
COVERAGE := 80
DOCKER_BUILDKIT := 1
COMPOSE_DOCKER_CLI_BUILD := 1


help: ## Show this help (runs only in bash)
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

test_service_base: ## run base tests for all services
	docker run --rm --network $(DOCKER_NETWORK) user_service bash -lce "pytest ./services/base/tests/ --verbose \
		--cov=services/base/ "

test_api_layer: ## run user_service integration tests
	docker run --rm --network $(DOCKER_NETWORK) user_service bash -lce "pytest ./services/user_service/tests/api --verbose -vv "

test_application_layer: ## run user_service unit tests
	docker run --rm --network $(DOCKER_NETWORK) user_service bash -lce "pytest ./services/user_service/tests/application/ --verbose -vv  \
	--cov=services/user_service/ "

test_user_service: test_service_base test_api_layer test_application_layer ## run tests specific to user service
