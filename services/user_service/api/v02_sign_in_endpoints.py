import json
import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Body, <PERSON>ie, Depends, Form, Response
from fastapi_injector import Injected
from jose import jwt
from starlette.responses import RedirectResponse

from services.base.api.auth_params_response import AuthParamsResponse
from services.base.api.authentication.auth_guard import AuthGuard, get_current_uuid, get_current_uuid_or_none
from services.base.api.authentication.exceptions import InvalidCredentialsException
from services.base.api.authentication.token_handling import (
    generate_access_token,
    generate_refresh_token,
    get_uuid_from_token,
)
from services.base.api.message_response import SingleMessageResponse
from services.base.application.authorization_encryption import decrypt
from services.base.application.constants import UserTokenKeys
from services.base.application.exceptions import BadRequestException
from services.base.domain.enums.client_apps import DEFAULT_CLIENT_APP, ClientApps
from services.base.domain.enums.provider import SupportedLoginProviders
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.user_service.api.boundaries.logout_input_boundary import LogoutInputBoundary
from services.user_service.api.boundaries.sign_in_with_apple_mobile_input_boundary import (
    SignInWithAppleMobileInputBoundary,
)
from services.user_service.api.constants import V02SignInEndpointRoutes
from services.user_service.api.output_models.member_user_api_output import MemberUserOutput, MemberUserSettingsOutput
from services.user_service.api.request_models.sign_in_with_recover_request_input import SignInWithRecoverRequestInput
from services.user_service.api.response_models.member_user_sign_in_response import (
    MemberUserSignInResponse,
    generate_member_user_sign_in_response,
)
from services.user_service.api.responses.sign_in_response import generate_sign_in_response
from services.user_service.api.utils.validate_apple_request_origin import (
    validate_apple_request_origin,
)
from services.user_service.application.authorizers.apple import AppleOAuth2Authorizer
from services.user_service.application.authorizers.google import GoogleOAuth2Authorizer
from services.user_service.application.boundaries.apple_authorize_callback import AppleAuthorizeCallback, AppleUserModel
from services.user_service.application.boundaries.auth_by_code_input import AuthByCodeInput
from services.user_service.application.use_cases.auth_use_cases.create_anonymous_user_use_case import (
    CreateAnonymousUserUseCase,
)
from services.user_service.application.use_cases.auth_use_cases.enums.sign_in_action_type import SignInActionType
from services.user_service.application.use_cases.auth_use_cases.recover_anonymous_user_use_case import (
    RecoverAnonymousUserUseCase,
)
from services.user_service.application.use_cases.notification_inbox.unlink_device_os_notifications import (
    UnlinkDeviceOSNotificationsUseCase,
)
from services.user_service.domain.providers.apple.constants import AppleIdTokenKeys, AppleUserObjectKeys
from settings.app_config import settings

v02_sign_in_router = APIRouter(
    prefix="/api/v0.2/auth",
    tags=["auth"],
    responses={404: {"description": "Not found"}},
)


@v02_sign_in_router.get(V02SignInEndpointRoutes.SIGN_IN_URL)
def get_login_auth_url_endpoint(
    provider: SupportedLoginProviders,
    redirect_uri: str,
    nonce: Optional[str] = None,
    client: ClientApps = DEFAULT_CLIENT_APP,
    google_authorizer: GoogleOAuth2Authorizer = Injected(GoogleOAuth2Authorizer),
    apple_authorizer: AppleOAuth2Authorizer = Injected(AppleOAuth2Authorizer),
    state: str = "",
) -> SingleMessageResponse:
    """
    Generates authentication URL for the external provider
    """
    match provider:
        case SupportedLoginProviders.GOOGLE:
            authorizer = google_authorizer
        case SupportedLoginProviders.APPLE:
            authorizer = apple_authorizer

    auth_url = authorizer.build_login_auth_url(
        redirect_uri=redirect_uri, client=client, state=state, provider=provider, nonce=nonce
    )

    # For ALL providers (for now):
    return SingleMessageResponse(message=auth_url)


@v02_sign_in_router.get(V02SignInEndpointRoutes.SIGN_IN_PARAMETERS)
def get_login_auth_parameters_endpoint(
    provider: SupportedLoginProviders,
    client: ClientApps = DEFAULT_CLIENT_APP,
    state: str = "",
    google_authorizer: GoogleOAuth2Authorizer = Injected(GoogleOAuth2Authorizer),
    apple_authorizer: AppleOAuth2Authorizer = Injected(AppleOAuth2Authorizer),
) -> AuthParamsResponse:
    """
    Get authentication params for the external provider
    """
    match provider:
        case SupportedLoginProviders.GOOGLE:
            authorizer = google_authorizer
        case SupportedLoginProviders.APPLE:
            authorizer = apple_authorizer

    auth_parameters_dict = authorizer.get_login_auth_parameters(client=client, state=state, provider=provider)

    # For ALL providers (for now):
    return AuthParamsResponse(**auth_parameters_dict)


@v02_sign_in_router.post(V02SignInEndpointRoutes.BY_CODE, response_model=MemberUserSignInResponse)
async def register_or_login_user_by_code_endpoint(
    provider: SupportedLoginProviders,
    input_data: AuthByCodeInput,
    should_link_account: bool = False,
    user_uuid: UUID = Depends(get_current_uuid_or_none),
    member_user_settings_repository: MemberUserSettingsRepository = Injected(MemberUserSettingsRepository),
    google_authorizer: GoogleOAuth2Authorizer = Injected(GoogleOAuth2Authorizer),
    apple_authorizer: AppleOAuth2Authorizer = Injected(AppleOAuth2Authorizer),
) -> Response:
    """
    Registers or logins a user by authorization code
    Returns:
        response with api_access_token in the body and api_refresh_token in the http only secure cookie
    """
    match provider:
        case SupportedLoginProviders.GOOGLE:
            authorizer = google_authorizer
        case SupportedLoginProviders.APPLE:
            authorizer = apple_authorizer

    user, action_type = await authorizer.register_or_login_user(
        client=input_data.client,
        response_body=input_data,
        provider=provider,
        user_uuid=user_uuid if should_link_account else None,
    )

    response = generate_sign_in_response(
        sign_in_response_model=await generate_member_user_sign_in_response(
            user=user, action_type=action_type, settings_repo=member_user_settings_repository
        ),
    )
    return response


@v02_sign_in_router.post(V02SignInEndpointRoutes.ANONYMOUSLY, response_model=MemberUserSignInResponse)
async def sign_in_anonymous_user_endpoint(
    request_input: SignInWithRecoverRequestInput | None = Body(default=None),
    user_uuid: UUID | None = Depends(get_current_uuid_or_none),
    create_use_case: CreateAnonymousUserUseCase = Injected(CreateAnonymousUserUseCase),
    recover_use_case: RecoverAnonymousUserUseCase = Injected(RecoverAnonymousUserUseCase),
) -> Response:
    """
    Returns:
        response with user, status and api_access_token in the body and api_refresh_token in the http only secure cookie
    """
    if user_uuid:
        raise BadRequestException(message="user already signed in")

    if request_input:
        output = await recover_use_case.execute_async(recovery_token=request_input.recovery_token)
        user, user_settings, recovery_token, action_type = output.user, output.settings, None, SignInActionType.SIGNED
    else:
        output = await create_use_case.execute_async()
        user, user_settings, recovery_token, action_type = (
            output.user,
            output.settings,
            output.recovery_token,
            SignInActionType.REGISTERED,
        )

    response = generate_sign_in_response(
        sign_in_response_model=MemberUserSignInResponse(
            user=MemberUserOutput(
                **user.model_dump(by_alias=True), settings=MemberUserSettingsOutput.map(model=user_settings)
            ),
            api_access_token=generate_access_token(
                user_uuid=user.user_uuid,
                time_delta=timedelta(minutes=settings.API_ACCESS_TOKEN_EXPIRATION_TIME_MINUTES),
            ),
            action_type=action_type,
            recovery_token=recovery_token,
        ),
        refresh_token=generate_refresh_token(
            user_uuid=user.user_uuid,
            time_delta=timedelta(days=100_000),
        ),
    )
    return response


@v02_sign_in_router.post(
    V02SignInEndpointRoutes.WITH_APPLE_CALLBACK,
    response_model=MemberUserSignInResponse,
    dependencies=[Depends(validate_apple_request_origin)],
)
async def sign_in_with_apple_callback(
    user: Optional[str] = Form(default=None),
    id_token: str = Form(...),
    code: str = Form(...),
    state: Optional[str] = Form(default=None),
    member_user_settings_repository: MemberUserSettingsRepository = Injected(MemberUserSettingsRepository),
    apple_authorizer: AppleOAuth2Authorizer = Injected(AppleOAuth2Authorizer),
) -> RedirectResponse:
    """
    Registers or logins a user by apple form post callback, should be used by web client.
    Returns:
        Redirect response with api_access_token in the body and api_refresh_token in the http only secure cookie
    """

    user_model = None
    if user:
        apple_user_dict: dict = dict(json.loads(user))

        first_name = apple_user_dict.get(AppleUserObjectKeys.NAME.value, {}).get(
            AppleUserObjectKeys.FIRST_NAME.value, None
        )
        last_name = apple_user_dict.get(AppleUserObjectKeys.NAME.value, {}).get(
            AppleUserObjectKeys.LAST_NAME.value, None
        )
        email = apple_user_dict.get(AppleIdTokenKeys.EMAIL.value, None)

        user_model = AppleUserModel(
            first_name=first_name,
            last_name=last_name,
            email=email,
        )

    body = AppleAuthorizeCallback(
        user=user_model,
        id_token=id_token,
        code=code,
    )
    redirect_uri = state or settings.AFTER_LOGIN_IOS_REDIRECT_URL

    token_payload = jwt.get_unverified_claims(token=id_token)
    user_uuid = UUID(decrypt(token=token_payload["nonce"])) if token_payload.get("nonce") else None

    user, action_type = await apple_authorizer.register_or_login_user(
        client=ClientApps.WEB,
        response_body=body,
        provider=SupportedLoginProviders.APPLE,
        user_uuid=user_uuid,
    )

    redirect_response = generate_sign_in_response(
        sign_in_response_model=await generate_member_user_sign_in_response(
            user=user, action_type=action_type, settings_repo=member_user_settings_repository
        ),
        redirect_uri=redirect_uri,
    )
    return redirect_response


@v02_sign_in_router.post(V02SignInEndpointRoutes.WITH_APPLE_MOBILE, response_model=MemberUserSignInResponse)
async def sign_in_with_apple_mobile(
    user_data: SignInWithAppleMobileInputBoundary,
    user_uuid: UUID | None = Depends(get_current_uuid_or_none),
    should_link_account: bool = False,
    member_user_settings_repository: MemberUserSettingsRepository = Injected(MemberUserSettingsRepository),
    apple_authorizer: AppleOAuth2Authorizer = Injected(AppleOAuth2Authorizer),
) -> RedirectResponse:
    """Sign in endpoint that should be used to log in via Apple on the iOS clients."""
    body = AppleAuthorizeCallback(user=user_data.user, id_token=user_data.id_token, code=user_data.code)
    user, action_type = await apple_authorizer.register_or_login_user(
        client=ClientApps.IOS,
        response_body=body,
        provider=SupportedLoginProviders.APPLE,
        user_uuid=user_uuid if should_link_account else None,
    )

    response = generate_sign_in_response(
        sign_in_response_model=await generate_member_user_sign_in_response(
            user=user, action_type=action_type, settings_repo=member_user_settings_repository
        ),
        redirect_uri=None,
    )
    return response


@v02_sign_in_router.post(V02SignInEndpointRoutes.LOGOUT)
async def logout_endpoint(
    response: Response,
    logout_input: LogoutInputBoundary,
    unlink_os_notifications_use_case: UnlinkDeviceOSNotificationsUseCase = Injected(UnlinkDeviceOSNotificationsUseCase),
    user_uuid: UUID = Depends(get_current_uuid),
) -> SingleMessageResponse:
    """
    Logouts user by deleting his refresh token cookie. Unlinks device os notifications if device_id is provided.
    """
    message = "successfully logged out"
    if logout_input.device_id:
        logging.info(f"unsubscribing push notifications for device: {logout_input.device_id}")
        try:
            await unlink_os_notifications_use_case.execute_async(
                device_token=logout_input.device_id,
                user_uuid=user_uuid,
            )
            logging.info("unsubscribed from push notifications")
        except Exception as error:
            logging.exception(error)

    response.delete_cookie(key=UserTokenKeys.API_REFRESH_TOKEN)
    logout_response = SingleMessageResponse(message=message)
    return logout_response


@v02_sign_in_router.post(V02SignInEndpointRoutes.REFRESH)
async def refresh_endpoint(
    response: Response,
    api_refresh_token: str = Cookie(None),
    member_user_repository: MemberUserRepository = Injected(MemberUserRepository),
    auth_guard: AuthGuard = Depends(),
):
    """
    Refreshes user access token on provided valid refresh token.
    """
    decoded_refresh_token = auth_guard.validate_refresh_token(api_refresh_token=api_refresh_token)
    user = await member_user_repository.get_by_uuid(get_uuid_from_token(decoded_token=decoded_refresh_token))
    if user is None:
        raise InvalidCredentialsException("User not found.")
    _ = await auth_guard.create_authorization_response(
        user=user,
        refresh_expiration_timestamp=float(decoded_refresh_token[UserTokenKeys.EXPIRATION_TIME]),
        response=response,
    )
    return SingleMessageResponse(message="Successfully refreshed.")
