from typing import Dict, Optional

from fastapi import APIRouter
from fastapi_injector import Injected

from services.base.domain.enums.client_apps import ClientApps
from services.base.domain.enums.provider import SupportedLoginProviders
from services.user_service.api.constants import SignInEndpointRoutes
from services.user_service.api.response_models.get_sign_in_parameters_response import GetSignInParametersResponse
from services.user_service.application.authorizers.apple import AppleOAuth2Authorizer
from services.user_service.application.use_cases.auth_use_cases.get_sign_in_parameters_use_case import (
    GetSignInParametersUseCase,
)
from services.user_service.application.use_cases.auth_use_cases.get_sign_in_url_use_case import GetSignInUrlUseCase
from services.user_service.application.use_cases.auth_use_cases.providers.google.google_authorizer import (
    GoogleOAuth2Authorizer,
)
from services.user_service.application.use_cases.auth_use_cases.sign_in_authorizer import SignInAuthorizer

sign_in_router = APIRouter(
    prefix="/api/v1.0/sign_in",
    tags=["sign_in"],
    responses={404: {"description": "Not found"}},
)

sign_in_providers: Dict[SupportedLoginProviders, SignInAuthorizer] = {
    SupportedLoginProviders.GOOGLE: GoogleOAuth2Authorizer(),
    SupportedLoginProviders.APPLE: AppleOAuth2Authorizer(),
}


@sign_in_router.get(SignInEndpointRoutes.SIGN_IN_URL)
def get_sign_in_url_endpoint(
    provider: SupportedLoginProviders,
    client: ClientApps,
    nonce: Optional[str] = None,
    state: Optional[str] = None,
    use_case: GetSignInUrlUseCase = Injected(GetSignInUrlUseCase),
) -> str:
    """
    Get sign in URL for the external provider
    """
    return use_case.execute(authorizer=sign_in_providers[provider], client=client, state=state, nonce=nonce)


@sign_in_router.get(SignInEndpointRoutes.SIGN_IN_PARAMETERS, response_model_exclude_none=True)
def get_sign_in_parameters_endpoint(
    provider: SupportedLoginProviders,
    client: ClientApps,
    state: Optional[str] = None,
    nonce: Optional[str] = None,
    use_case: GetSignInParametersUseCase = Injected(GetSignInParametersUseCase),
) -> GetSignInParametersResponse:
    """
    Get sign in parameters for the external provider
    """

    output = use_case.execute(authorizer=sign_in_providers[provider], client=client, state=state, nonce=nonce)
    return GetSignInParametersResponse.map(model=output)
